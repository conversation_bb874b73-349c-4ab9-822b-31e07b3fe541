/* Table View Styles - Light Theme */

/* Minor layout tweaks specific to table view sections */
html, body { 
    height: 100%; 
}

body { 
    margin: 0; 
    display: flex; 
    flex-direction: column; 
    min-height: 100vh; 
    overflow: hidden; 
}

.tableview-sections {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: 20px;
    align-items: start;
    padding: 16px;
    max-width: 100%;
}

.tableview-section {
    background: #ffffff;
    border-radius: 6px;
    box-shadow: var(--card-shadow, 0 1px 3px rgba(0,0,0,0.15));
    overflow: hidden;
    min-width: 0;
    max-width: 100%;
}

.tableview-section h3 {
    margin: 0;
    padding: 8px 12px;
    background: #f8f9fa;
    border-bottom: 1px solid rgba(0,0,0,0.08);
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 16px;
}

.tableview-section .hostlist-table { 
    margin: 0; 
    table-layout: fixed; 
    width: 100%; 
    border-collapse: collapse;
}

.tableview-section .hostlist-table th,
.tableview-section .hostlist-table td {
    padding: 6px 8px;
    text-align: left;
    vertical-align: middle;
    word-wrap: break-word;
    overflow-wrap: break-word;
    font-size: 13px;
}

/* Center status and services columns */
.tableview-section .hostlist-table .col-status,
.tableview-section .hostlist-table .col-service {
    text-align: center;
}

.tableview-section .hostlist-table th {
    background: #f8f9fa;
    font-weight: 600;
    border-bottom: 1px solid rgba(0,0,0,0.1);
    font-size: 12px;
}

.svc-badges {
    display: inline-flex; 
    gap: 4px; 
    align-items: center; 
    flex-wrap: wrap;
    margin: 1px 0;
}

.svc-badge { 
    padding: 2px 6px; 
    border-radius: 8px; 
    font-size: 10px; 
    font-weight: 500;
    white-space: nowrap;
}

.svc-badge.ok { 
    background: #a5d6a7; 
    color: #1b5e20; 
}

.svc-badge.warning { 
    background: #ffe082; 
    color: #8d6e00; 
}

.svc-badge.critical { 
    background: #ef9a9a; 
    color: #b71c1c; 
}

.svc-badge.unknown { 
    background: #b0bec5; 
    color: #263238; 
}

.tableview-empty { 
    padding: 24px; 
    text-align: center; 
    opacity: 0.7; 
}

.hostlist-status { 
    display: inline-block; 
    white-space: nowrap; 
    max-width: none; 
    padding: 3px 6px;
    border-radius: 4px;
    font-weight: 500;
    font-size: 12px;
}

.hostlist-container { 
    flex: 1; 
    overflow: auto; 
    padding: 0; 
    margin: 0;
    display: flex;
    flex-direction: column;
}

.tableview-content { 
    padding: 4px; 
    margin: 0;
    overflow-x: auto;
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* Loading indicator centering for tableview */
.tableview-content .hostlist-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 400px;
    color: #666666;
    gap: 20px;
}

/* Custom scrollbar for light theme */
.tableview-content::-webkit-scrollbar {
    width: 8px;
}

.tableview-content::-webkit-scrollbar-track {
    background: #f5f5f5;
    border-radius: 4px;
}

.tableview-content::-webkit-scrollbar-thumb {
    background: #e0e0e0;
    border-radius: 4px;
    transition: background 0.2s ease;
}

.tableview-content::-webkit-scrollbar-thumb:hover {
    background: #d0d0d0;
}

.tableview-content::-webkit-scrollbar-thumb:active {
    background: #c0c0c0;
}

/* Host cell content styling */
.host-cell-content {
    display: flex;
    align-items: center;
    gap: 4px;
    min-width: 0;
    overflow: hidden;
}

.host-cell-content .hostname {
    flex: 1;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 13px;
}

/* Only highlight hostname when down */
.host-cell-content .hostname.down {
    color: #d32f2f;
    font-weight: 600;
}

.host-cell-content .graph-icon {
    flex-shrink: 0;
    margin-right: 2px;
    font-size: 12px;
}

/* Status column styling */
.col-status .hostlist-status {
    display: inline-block;
    padding: 3px 6px;
    border-radius: 4px;
    font-weight: 500;
    text-align: center;
    min-width: 50px;
    font-size: 12px;
}

/* Service badges container */
.col-service .svc-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 3px;
    align-items: center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .tableview-sections {
        grid-template-columns: 1fr;
        gap: 12px;
        padding: 8px;
    }
    .tableview-section .hostlist-table th,
    .tableview-section .hostlist-table td {
        padding: 5px 6px;
    }
}

@media (max-width: 480px) {
    .tableview-sections {
        padding: 6px;
    }
    .tableview-section h3 {
        padding: 8px 10px;
        font-size: 13px;
    }
}
