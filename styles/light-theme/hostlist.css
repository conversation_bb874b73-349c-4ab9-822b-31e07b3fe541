/* Host List Styles - Light Theme */

/* Standalone Host List Page Styles */
.hostlist-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 112px); /* Adjust for header + search bar */
    background-color: #ffffff;
    overflow: hidden;
}

@media (max-width: 768px) {
    .hostlist-container {
        height: calc(100vh - 140px); /* Adjust for mobile header + search bar */
        overflow-x: hidden;
        overflow-y: auto;
    }
}

@media (max-width: 768px) {
    /* With mobile status container, adjust height further */
    .hostlist-container {
        height: calc(100vh - 230px); /* Account for mobile header + search bar + mobile status container */
    }
}

.hostlist-page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
    background-color: #403c3c;
}

.hostlist-page-header h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #ffffff;
}

.hostlist-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #e8e8e8;
    background-color: #403c3c;
}

.hostlist-header h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #ffffff;
}

/* Status icon styles */
.status-icon {
    font-size: 14px;
    margin-left: 6px;
    vertical-align: middle;
}

.status-icon.ok {
    color: #4CAF50;
}

.status-icon.down {
    color: #F44336;
}

.status-icon.unknown {
    color: #64748b;
}

.status-icon.warning {
    color: #FFC107;
}

.status-icon.critical {
    color: #F44336;
}

.status-icon.pending {
    color: #9E9E9E;
}

/* Header Status Filters - Style like hostCount.js */
.hostlist-status-filters-header {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-left: 15px;
    align-items: center;
}

/* Auto-refresh countdown styles */
.refresh-countdown {
    display: flex;
    align-items: center;
    margin-left: 15px;
    font-size: 14px;
    color: #aaa;
    gap: 5px;
    border-left: 1px solid rgba(255, 255, 255, 0.2);
    padding-left: 10px;
}

.refresh-countdown i {
    color: #aaa;
}

.refresh-countdown.refreshing i {
    animation: spin 1s linear infinite;
}

/* Prevent non-refresh icons inside the countdown from spinning */
.refresh-countdown.refreshing i:not(.fa-refresh) {
    animation: none;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.hostlist-filter-section-header {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-right: 15px;
}

.hostlist-filter-section-header .filter-label {
    font-size: 12px;
    color: #ffffff;
    margin-right: 8px;
    font-weight: 600;
}

.hostlist-status-filters-header .hostlist-status-filter {
    cursor: pointer;
    position: relative;
    transition: transform 0.2s, box-shadow 0.2s;
    border-radius: 50%;
    padding: 0;
    margin: 0 2px;
    width: 24px;
    height: 24px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border: 1px solid rgba(0,0,0,0.2);
    color: white;
    font-size: 11px;
    font-weight: bold;
}

/* All button with text */
.hostlist-status-filters-header .hostlist-status-filter.all {
    border-radius: 20px;
    width: auto;
    height: 24px;
    padding: 0 10px;
    background-color: #555;
    font-size: 12px;
}

.hostlist-status-filters-header .hostlist-status-filter:hover {
    transform: scale(1.1);
    box-shadow: 0 0 5px rgba(0,0,0,0.2);
}

.hostlist-status-filters-header .hostlist-status-filter.active {
    box-shadow: 0 0 0 1px #fff, 0 0 0 2px currentColor;
    transform: scale(1.1);
}

.hostlist-status-filters-header .hostlist-status-filter.ok {
    background-color: #4CAF50;
}

.hostlist-status-filters-header .hostlist-status-filter.down {
    background-color: #F44336;
}

.hostlist-status-filters-header .hostlist-status-filter.unknown {
    background-color: #64748b;
}

.hostlist-status-filters-header .hostlist-status-filter.warning {
    background-color: #FFC107;
}

.hostlist-status-filters-header .hostlist-status-filter.critical {
    background-color: #F44336;
}

.hostlist-status-filters-header .hostlist-status-filter.pending {
    background-color: #9E9E9E;
}

/* Modal Filter Sections */
.hostlist-filter-sections {
    display: flex;
    gap: 20px;
    margin-top: 10px;
    flex-wrap: wrap;
}

.hostlist-filter-section {
    flex: 1;
    min-width: 300px;
}

.hostlist-filter-section h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.filter-help {
    font-size: 11px;
    font-weight: normal;
    color: #777;
    margin-left: 8px;
}

.hostlist-status-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.hostlist-status-filters .hostlist-status-filter {
    padding: 6px 12px;
    border: 1px solid #e8e8e8;
    background-color: #fafafa;
    color: #333;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
    position: relative;
}

.hostlist-status-filters .hostlist-status-filter:hover {
    background-color: #f5f5f5;
    border-color: #e0e0e0;
}

.hostlist-status-filters .hostlist-status-filter.active {
    background-color: #b4ca45;
    color: white;
    border-color: #a1b83e;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
}

/* Different colors for different status buttons when active */
.hostlist-status-filters .hostlist-status-filter.ok.active {
    background-color: #4CAF50;
    border-color: #388E3C;
}

.hostlist-status-filters .hostlist-status-filter.warning.active {
    background-color: #FFC107;
    border-color: #FFA000;
    color: #333;
}

.hostlist-status-filters .hostlist-status-filter.critical.active {
    background-color: #F44336;
    border-color: #D32F2F;
}

.hostlist-status-filters .hostlist-status-filter.unknown.active {
    background-color: #64748b;
    border-color: #536478;
}

.hostlist-status-filters .hostlist-status-filter.pending.active {
    background-color: #9E9E9E;
    border-color: #757575;
}

.hostlist-filters {
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
    background-color: #fafafa;
}

.hostlist-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.hostlist-refresh {
    background-color: transparent;
    border: none;
    font-size: 18px;
    color: #dddddd;
    cursor: pointer;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.hostlist-refresh:hover {
    color: #ffffff;
    background-color: rgba(255, 255, 255, 0.1);
}

.hostlist-close {
    background-color: transparent;
    border: none;
    font-size: 24px;
    color: #dddddd;
    cursor: pointer;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.hostlist-close:hover {
    color: #ffffff;
    background-color: rgba(255, 255, 255, 0.1);
}

/* Full-width search bar at top of page */
.hostlist-search-container {
    display: flex;
    align-items: center;
    background-color: #403c3c;
    padding: 8px 15px;
    position: relative;
    border-bottom: 1px solid #555;
    z-index: 9; /* Just below the thead */
    gap: 10px;
}

.search-input-wrapper {
    position: relative;
    flex: 0 1 300px;
    display: flex;
    align-items: center;
}

.search-mode-selector {
    min-width: 120px;
}

.search-mode-selector select {
    background-color: #333;
    border: none;
    border-radius: 4px;
    color: #ffffff;
    padding: 8px 10px;
    font-size: 14px;
    height: 36px; /* Match the height of the search input */
    width: 100%;
    cursor: pointer;
}

.hostgroup-selector {
    min-width: 150px;
}

.hostgroup-selector select {
    background-color: #333;
    border: none;
    border-radius: 4px;
    color: #ffffff;
    padding: 8px 10px;
    font-size: 14px;
    height: 36px; /* Match the height of the search input */
    width: 100%;
    cursor: pointer;
}

.servicegroup-selector {
    min-width: 150px;
}

.servicegroup-selector select {
    background-color: #333;
    border: none;
    border-radius: 4px;
    color: #ffffff;
    padding: 8px 10px;
    font-size: 14px;
    height: 36px; /* Match the height of the search input */
    width: 100%;
    cursor: pointer;
}

.hostlist-search-container input {
    flex: 1;
    background-color: #333;
    border: none;
    border-radius: 4px;
    color: #ffffff;
    padding: 8px 40px 8px 15px;
    font-size: 14px;
    height: 22px;
}

.hostlist-search-container input:focus {
    outline: none;
    background-color: #444;
}

.hostlist-search-container input::placeholder {
    color: #ccc;
}

.search-actions {
    display: flex;
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    align-items: center;
}

.hostlist-search-container .hostlist-clear-search {
    background: transparent;
    border: none;
    color: #ccc;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
}

.hostlist-search-container .hostlist-clear-search:hover {
    color: #fff;
}

/* Update refresh styles to also apply to CSV export button */
.hostlist-search-container .hostlist-refresh,
.hostlist-search-container .export-csv-btn {
    background: transparent;
    border: none;
    color: #ccc;
    font-size: 16px;
    cursor: pointer;
    margin-left: 5px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
}

.hostlist-search-container .hostlist-refresh:hover,
.hostlist-search-container .export-csv-btn:hover {
    color: #fff;
}

/* Old search styles can be removed */
.hostlist-page-header .search-container {
    display: none;
}

.hostlist-content {
    flex: 1;
    overflow-y: auto;
    padding: 0;
    padding-bottom: 12px;
    margin: 0; /* Remove any margins */
    background-color: #ffffff;
    position: relative;
    border-top: none; /* Remove any border */
}

/* Custom scrollbar for light theme */
.hostlist-content::-webkit-scrollbar {
    width: 8px;
}

.hostlist-content::-webkit-scrollbar-track {
    background: #f5f5f5;
    border-radius: 4px;
}

.hostlist-content::-webkit-scrollbar-thumb {
    background: #e0e0e0;
    border-radius: 4px;
    transition: background 0.2s ease;
}

.hostlist-content::-webkit-scrollbar-thumb:hover {
    background: #d0d0d0;
}

.hostlist-content::-webkit-scrollbar-thumb:active {
    background: #c0c0c0;
}

.hostlist-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #777;
    gap: 20px;
}

.hostlist-loading .spinner {
    /* Simple single-ring spinner */
    width: 36px;
    height: 36px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-top-color: #b4ca45; /* accent */
    border-radius: 50%;
    animation: spin 1s linear infinite;
    position: static;
}

/* Hide previously added inner ring */
.hostlist-loading .spinner::before {
    content: none;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.hostlist-empty, .hostlist-error, .hostlist-empty-results {
    padding: 40px 20px;
    text-align: center;
    color: #777;
    font-size: 16px;
}

.hostlist-error {
    color: #d41d28;
}

/* Updated table styles to match the dark theme structure */
.hostlist-table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    font-size: 14px;
    table-layout: fixed;
}

.hostlist-table thead {
    position: sticky;
    top: -1px; /* Negative offset to ensure no gap */
    z-index: 10; /* Higher z-index to ensure it stays on top */
    background-color: #f5f5f5;
    padding-top: 1px; /* Compensate for negative top */
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1); /* Add shadow for visual separation */
}

.hostlist-table th {
    position: relative;
    background-color: #f5f5f5;
    color: #333;
    font-weight: 600;
    border-bottom: 2px solid #e8e8e8;
    cursor: pointer;
    padding: 6px 15px; /* Reduced padding to make header smaller */
}

.hostlist-table th, 
.hostlist-table td {
    padding: 10px 15px;
    text-align: left;
    vertical-align: middle;
    border: 1px solid rgba(0, 0, 0, 0.1); /* Add borders between cells */
}

.hostlist-table td {
    border-bottom: 1px solid #e8e8e8;
    color: #333;
    word-wrap: break-word;
    text-overflow: ellipsis;
    overflow: hidden;
}

/* Alternating row colors */
.hostlist-table tr:nth-child(even) {
    background-color: rgba(0, 0, 0, 0.02);
}

.hostlist-table tr:nth-child(odd) {
    background-color: transparent;
}

/* Remove or hide sort icons */
.hostlist-table th i.fa-sort {
    display: none;
}

/* Minimum column widths instead of fixed widths */
.hostlist-table th:nth-child(1),
.hostlist-table td:nth-child(1) {
    min-width: 120px;
    width: auto;
}

.hostlist-table th:nth-child(2),
.hostlist-table td:nth-child(2) {
    min-width: 140px;
    width: auto;
}

.hostlist-table th:nth-child(3),
.hostlist-table td:nth-child(3) {
    min-width: 100px;
    width: auto;
}

.hostlist-table th:nth-child(4),
.hostlist-table td:nth-child(4) {
    min-width: 150px;
    width: auto;
}

.hostlist-table th:nth-child(5),
.hostlist-table td:nth-child(5) {
    min-width: 100px;
    width: auto;
}

.hostlist-table th:nth-child(6),
.hostlist-table td:nth-child(6) {
    min-width: 80px;
    width: auto;
}

.hostlist-host-row, .hostlist-service-row {
    cursor: pointer;
    transition: background-color 0.15s ease;
}

.hostlist-host-row {
    background-color: #ffffff;
    font-weight: 500;
}

/* Host row background colors for different statuses */
.hostlist-host-row[data-status="down"] {
    background-color: rgba(212, 29, 40, 0.1);
}

.hostlist-host-row[data-status="unknown"] {
    background-color: rgba(74, 127, 190, 0.1);
}

/* Override hover effects for down/unknown hosts to maintain status visibility */
.hostlist-table .hostlist-host-row[data-status="down"]:hover {
    background-color: rgba(212, 29, 40, 0.15) !important;
}

.hostlist-table .hostlist-host-row[data-status="unknown"]:hover {
    background-color: rgba(74, 127, 190, 0.15) !important;
}

/* Override expanded state for down/unknown hosts */
.hostlist-host-row[data-status="down"].expanded {
    background-color: rgba(212, 29, 40, 0.12);
}

.hostlist-host-row[data-status="unknown"].expanded {
    background-color: rgba(74, 127, 190, 0.12);
}

.hostlist-table .hostlist-host-row[data-status="down"].expanded:hover {
    background-color: rgba(212, 29, 40, 0.18) !important;
}

.hostlist-table .hostlist-host-row[data-status="unknown"].expanded:hover {
    background-color: rgba(74, 127, 190, 0.18) !important;
}

.hostlist-table .hostlist-host-row:hover {
    background-color: #f0f0f0 !important;
}

.hostlist-host-row.expanded {
    background-color: #f5f5f5;
}

.hostlist-table .hostlist-host-row.expanded:hover {
    background-color: #f0f0f0 !important;
}

.hostlist-service-row {
    background-color: #fafafa;
}

.hostlist-table .hostlist-service-row:hover {
    background-color: #f5f5f5 !important;
}

.hostlist-service-row.hidden {
    display: table-row !important;
}

.hostlist-host-row.filtered-out,
.hostlist-service-row.filtered-out {
    display: none;
}

.hostlist-host-name {
    font-weight: 600;
    word-wrap: break-word;
    display: flex;
    align-items: center;
    gap: 8px;
}

.host-info {
    display: flex;
    align-items: center;
    gap: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.hostlist-host-name .hostlist-status {
    margin-left: 8px;
    font-size: 11px;
    min-width: 60px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 20px;
    padding: 2px 6px;
}

.hostlist-service-name {
    word-wrap: break-word;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chart-icon {
    color: #0066cc;
    margin-right: 5px;
    font-size: 14px;
    display: inline-block;
    vertical-align: middle;
}

.hostlist-host-name a,
.hostlist-service-name a {
    color: #0066cc;
    text-decoration: none;
    transition: color 0.2s ease;
}

.hostlist-host-name a:hover,
.hostlist-service-name a:hover {
    color: #004c99;
    text-decoration: underline;
}

.hostlist-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 600;
    font-size: 12px;
    min-width: 60px;
    text-align: center;
    margin: 0 auto; /* Center the status badge */
}

.hostlist-status.ok {
    background-color: rgba(180, 202, 69, 0.2);
    color: #333;
}

.hostlist-status.down {
    background-color: rgba(212, 29, 40, 0.2);
    color: #d41d28;
}

.hostlist-status.unknown {
    background-color: rgba(74, 127, 190, 0.2);
    color: #4a7fbe;
}

.hostlist-status.warning {
    background-color: rgba(254, 201, 48, 0.2);
    color: #333;
}

.hostlist-status.critical {
    background-color: rgba(212, 29, 40, 0.2);
    color: #d41d28;
}

.hostlist-status.pending {
    background-color: rgba(128, 130, 130, 0.2);
    color: #555;
}

.hostlist-output {
    word-wrap: break-word;
}

/* Ensure last column can expand */
.hostlist-table th:last-child,
.hostlist-table td:last-child {
    width: auto;
    max-width: none;
}

/* Responsive styles */
@media (max-width: 992px) {
    .hostlist-page-header .search-container {
        min-width: 200px;
    }
    
    /* Make table container horizontally scrollable */
    .hostlist-content {
        overflow-x: auto;
    }
    
    /* Prevent table from being squeezed */
    .hostlist-table {
        min-width: 800px;
        table-layout: fixed;
    }
    
    /* Status indicators in small screens */
    .hostlist-status-indicators {
        flex-wrap: wrap;
        justify-content: flex-start;
        gap: 4px;
    }
}

@media (max-width: 768px) {
    .hostlist-header, .hostlist-page-header {
        padding: 12px 15px;
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .hostlist-header h2, .hostlist-page-header h2 {
        font-size: 18px;
    }
    
    .hostlist-controls {
        width: 100%;
    }
    
    .hostlist-page-header .search-container {
        width: 100%;
        min-width: 0;
    }
    
    /* Compact search controls on mobile */
    .hostlist-search-container {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 8px;
    }

    /* Search input takes full row */
    .search-input-wrapper {
        flex: 1 0 100%;
    }

    /* Place selectors side-by-side */
    .search-mode-selector,
    .hostgroup-selector,
    .servicegroup-selector {
        flex: 1 0 calc(50% - 4px);
        min-width: 0;
    }

    /* Keep export button inline */
    .export-csv {
        flex: 0 0 auto;
        display: flex;
        align-items: center;
    }
    
    /* Status indicators in mobile screens */
    .hostlist-status-indicators {
        margin-top: 5px;
        gap: 4px;
        flex-wrap: wrap;
        justify-content: flex-start;
    }
    
    .status-indicator {
        font-size: 10px !important;
        width: 18px !important;
        height: 18px !important;
        padding: 2px 4px !important;
    }
    
    .status-indicator i {
        font-size: 11px !important;
    }
    
    .hostlist-status-filters-header {
        flex-direction: column;
        align-items: flex-start;
        width: 100%;
    }
    
    .hostlist-filter-section-header {
        width: 100%;
        justify-content: flex-start;
        margin-right: 0;
        margin-bottom: 5px;
    }
    
    /* Move things from header to mobile container */
    .mobile-status-container {
        display: flex;
        gap: 10px;
        align-items: center;
        justify-content: center;
        text-align: center;
    }
    
    /* Ensure refresh icon is visible on mobile */
    .refresh-countdown i.fa-refresh {
        display: inline-block !important;
        visibility: visible !important;
        color: #dddddd !important;
        opacity: 1 !important;
    }
    
    /* Adjust status filters in mobile container */
    .mobile-status-container .hostlist-status-filters-header {
        margin-left: 0;
        flex-direction: column;
        width: 100%;
        align-items: center;
        justify-content: center;
    }
    
    /* Fix status cell alignment on mobile */
    .hostlist-table .col-status {
        text-align: center !important;
        vertical-align: middle !important;
        padding: 8px 4px !important;
    }
    
    .hostlist-status {
        display: block !important;
        margin: 0 auto !important;
        width: fit-content !important;
        max-width: 100% !important;
    }
    
    /* Ensure table cells have proper alignment */
    .hostlist-table td {
        vertical-align: middle !important;
    }
    
    /* Reduce padding on mobile for better fit */
    .hostlist-table th, 
    .hostlist-table td {
        padding: 6px 8px !important;
    }
}

/* Column alignment and sizing */
.col-host {
    font-weight: 600;
}

.col-service {
    font-weight: 500;
}

.col-status {
    text-align: center;
}

.col-lastcheck {
    text-align: left;
}

.col-duration {
    text-align: left;
}

.col-attempt {
    text-align: center;
}

.col-info {
    text-align: left;
    user-select: all;
}

/* Hostname coloring by status - highlight hostname span only */
.hostname {
    font-weight: 600;
    padding: 2px 4px;
    border-radius: 3px;
    cursor: pointer;
}

.hostname.ok {
    background-color: rgba(180, 202, 69, 0.2);
    color: #333;
}

.hostname.down {
    background-color: rgba(212, 29, 40, 0.2);
    color: #d41d28;
}

.hostname.unknown {
    background-color: rgba(74, 127, 190, 0.2);
    color: #4a7fbe;
}

.hostname.warning {
    background-color: rgba(254, 201, 48, 0.2);
    color: #333;
}

.hostname.critical {
    background-color: rgba(212, 29, 40, 0.2);
    color: #d41d28;
}

.hostname.pending {
    background-color: rgba(128, 130, 130, 0.2);
    color: #555;
}

/* Service row highlighting for non-OK statuses - highlight multiple cells including status */
.hostlist-service-row[data-status="warning"] .col-service,
.hostlist-service-row[data-status="warning"] .col-status,
.hostlist-service-row[data-status="warning"] .col-lastcheck,
.hostlist-service-row[data-status="warning"] .col-duration,
.hostlist-service-row[data-status="warning"] .col-attempt,
.hostlist-service-row[data-status="warning"] .col-info {
    background-color: rgba(254, 201, 48, 0.15);
    border-radius: 3px;
}

.hostlist-service-row[data-status="critical"] .col-service,
.hostlist-service-row[data-status="critical"] .col-status,
.hostlist-service-row[data-status="critical"] .col-lastcheck,
.hostlist-service-row[data-status="critical"] .col-duration,
.hostlist-service-row[data-status="critical"] .col-attempt,
.hostlist-service-row[data-status="critical"] .col-info {
    background-color: rgba(212, 29, 40, 0.15);
    border-radius: 3px;
}

.hostlist-service-row[data-status="unknown"] .col-service,
.hostlist-service-row[data-status="unknown"] .col-status,
.hostlist-service-row[data-status="unknown"] .col-lastcheck,
.hostlist-service-row[data-status="unknown"] .col-duration,
.hostlist-service-row[data-status="unknown"] .col-attempt,
.hostlist-service-row[data-status="unknown"] .col-info {
    background-color: rgba(74, 127, 190, 0.15);
    border-radius: 3px;
}

.hostlist-service-row[data-status="pending"] .col-service,
.hostlist-service-row[data-status="pending"] .col-status,
.hostlist-service-row[data-status="pending"] .col-lastcheck,
.hostlist-service-row[data-status="pending"] .col-duration,
.hostlist-service-row[data-status="pending"] .col-attempt,
.hostlist-service-row[data-status="pending"] .col-info {
    background-color: rgba(128, 130, 130, 0.15);
    border-radius: 3px;
}

.hostlist-service-row[data-status="down"] .col-service,
.hostlist-service-row[data-status="down"] .col-status,
.hostlist-service-row[data-status="down"] .col-lastcheck,
.hostlist-service-row[data-status="down"] .col-duration,
.hostlist-service-row[data-status="down"] .col-attempt,
.hostlist-service-row[data-status="down"] .col-info {
    background-color: rgba(212, 29, 40, 0.15);
    border-radius: 3px;
}

/* First service row highlighting (on host rows) for non-OK service statuses */
.hostlist-host-row[data-service-status="warning"] .col-service,
.hostlist-host-row[data-service-status="warning"] .col-status,
.hostlist-host-row[data-service-status="warning"] .col-lastcheck,
.hostlist-host-row[data-service-status="warning"] .col-duration,
.hostlist-host-row[data-service-status="warning"] .col-attempt,
.hostlist-host-row[data-service-status="warning"] .col-info {
    background-color: rgba(254, 201, 48, 0.15);
    border-radius: 3px;
}

.hostlist-host-row[data-service-status="critical"] .col-service,
.hostlist-host-row[data-service-status="critical"] .col-status,
.hostlist-host-row[data-service-status="critical"] .col-lastcheck,
.hostlist-host-row[data-service-status="critical"] .col-duration,
.hostlist-host-row[data-service-status="critical"] .col-attempt,
.hostlist-host-row[data-service-status="critical"] .col-info {
    background-color: rgba(212, 29, 40, 0.15);
    border-radius: 3px;
}

.hostlist-host-row[data-service-status="unknown"] .col-service,
.hostlist-host-row[data-service-status="unknown"] .col-status,
.hostlist-host-row[data-service-status="unknown"] .col-lastcheck,
.hostlist-host-row[data-service-status="unknown"] .col-duration,
.hostlist-host-row[data-service-status="unknown"] .col-attempt,
.hostlist-host-row[data-service-status="unknown"] .col-info {
    background-color: rgba(74, 127, 190, 0.15);
    border-radius: 3px;
}

.hostlist-host-row[data-service-status="pending"] .col-service,
.hostlist-host-row[data-service-status="pending"] .col-status,
.hostlist-host-row[data-service-status="pending"] .col-lastcheck,
.hostlist-host-row[data-service-status="pending"] .col-duration,
.hostlist-host-row[data-service-status="pending"] .col-attempt,
.hostlist-host-row[data-service-status="pending"] .col-info {
    background-color: rgba(128, 130, 130, 0.15);
    border-radius: 3px;
}

.hostlist-host-row[data-service-status="down"] .col-service,
.hostlist-host-row[data-service-status="down"] .col-status,
.hostlist-host-row[data-service-status="down"] .col-lastcheck,
.hostlist-host-row[data-service-status="down"] .col-duration,
.hostlist-host-row[data-service-status="down"] .col-attempt,
.hostlist-host-row[data-service-status="down"] .col-info {
    background-color: rgba(212, 29, 40, 0.15);
    border-radius: 3px;
}

/* Monochrome graph icon - no hover effect */
.graph-icon {
    color: #777;
    margin-left: 5px;
    font-size: 14px;
    cursor: pointer;
    transition: color 0.2s ease, transform 0.2s ease;
    pointer-events: auto; /* Allow clicks on graph icon */
}

.graph-icon:hover {
    color: #0066cc;
    transform: scale(1.2);
}

/* Service name styling - no longer individually clickable */
.service-name {
    display: inline-block;
    padding: 2px 4px;
    border-radius: 3px;
}

/* Remove individual hover effects since the whole row is clickable */
.hostname {
    pointer-events: none; /* Prevent individual clicks on hostname */
}

/* Remove individual hover effects for service names */
.service-name {
    pointer-events: none; /* Prevent individual clicks on service name */
}

/* Toggle services icon */
.toggle-services {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    margin-left: 8px;
    cursor: pointer;
    border-radius: 50%;
    transition: background-color 0.2s ease;
    pointer-events: auto; /* Allow clicks on toggle services */
}

.toggle-services:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.toggle-services i {
    font-size: 12px;
    color: #777;
    transition: transform 0.2s ease;
}

.hostlist-host-row.collapsed .toggle-services i {
    transform: rotate(0deg);
}

/* Hide collapsed service rows */
.hostlist-service-row.collapsed {
    display: none;
}

/* Status icons styling */
.hostlist-status-icons {
    display: inline-flex;
    gap: 4px;
    align-items: center;
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;
}

.hostlist-status-icon {
    font-size: 12px;
    color: var(--text-secondary);
    opacity: 0.7;
    pointer-events: none; /* Allow clicks to pass through to parent cell */
}

.hostlist-status-icon:hover {
    opacity: 1;
}

/* Ensure proper layout for host and service cells with icons */
.col-host, .col-service {
    position: relative;
    padding-right: 40px; /* Make room for status icons */
}

/* Override pointer-events for icons container */
.hostlist-status-icons {
    pointer-events: none;
}

td.col-status {
    text-align: center;
}

.hostlist-table td.col-status {
    text-align: center;
}

.status-filter-reset {
    margin-left: 10px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s, transform 0.2s;
    padding: 0;
    background: rgba(0,0,0,0.1);
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
}

.status-filter-reset:hover {
    opacity: 1;
    transform: rotate(90deg);
    background: rgba(0,0,0,0.2);
}

.status-filter-reset i {
    color: #ffffff;
    font-size: 14px;
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Remove old filter-reset class */
.filter-reset {
    display: none;
}

/* Host Status Indicators */
.hostlist-status-indicators {
    display: flex;
    background-color: transparent;
    padding: 0;
    gap: 10px;
    align-items: center; /* Ensures vertical alignment with other header items */
    flex-wrap: wrap;
    margin-left: 15px; 
    height: 26px; /* Match height of filter buttons for alignment */
}

.status-indicator {
    position: relative; 
    display: flex;
    align-items: center;
    justify-content: center; 
    width: 26px; 
    height: 26px; 
    border-radius: 50%; 
    cursor: pointer;
    transition: background-color 0.2s, box-shadow 0.2s;
    background-color: rgba(255,255,255,0.08);
    box-shadow: 0 0 0 1px rgba(255,255,255,0.15); 
}

.status-indicator:hover {
    background-color: rgba(255,255,255,0.18);
    box-shadow: 0 0 0 1px rgba(255,255,255,0.25);
}

.status-indicator i {
    color: #ccc; 
    font-size: 13px; 
    line-height: 26px; 
}

/* Badge Styles */
.indicator-badge {
    position: absolute;
    top: 0px;  /* Position badge at the very top of the icon */
    right: 0px; /* Position badge at the very right of the icon */
    transform: translate(40%, -40%); /* Nudge badge slightly outside and up */
    background-color: #F44336; 
    color: white;
    border-radius: 50%;
    font-size: 9px; 
    font-weight: bold;
    display: flex; /* For centering text in small circle */
    align-items: center;
    justify-content: center;
    width: 15px; /* Fixed small width */
    height: 15px; /* Fixed small height */
    text-align: center;
    display: none; /* Hidden by default */
    border: 1px solid #403c3c; /* Match header bg for badge border */
    box-sizing: border-box; /* Include padding and border in element's total width and height */
}

.status-indicator.active i {
    color: #b4ca45; /* Green for active */
}
.status-indicator.active .indicator-badge {
    display: none; 
}


.status-indicator.inactive i {
    color: #F44336; /* Red for inactive */
}
.status-indicator.inactive .indicator-badge {
    background-color: #F44336;
    display: flex; /* Use flex to show */
}

.status-indicator.warning i {
    color: #ffc107; /* Yellow for warning */
}
.status-indicator.warning .indicator-badge {
    background-color: #ffc107;
    color: #333; 
    display: flex; /* Use flex to show */
}

/* Host Status Popover */
.status-hosts-popover {
    position: absolute;
    background-color: #3a3a3a; 
    color: #f0f0f0; 
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
    padding: 8px 12px;
    min-width: 200px; 
    max-width: 300px; 
    max-height: 300px !important; 
    overflow-y: auto;
    z-index: 9999;
    display: none;
    border: 1px solid #555;
}

/* Custom scrollbar for status hosts popover */
.status-hosts-popover::-webkit-scrollbar {
    width: 6px;
}

.status-hosts-popover::-webkit-scrollbar-track {
    background: #4a4a4a;
    border-radius: 3px;
}

.status-hosts-popover::-webkit-scrollbar-thumb {
    background: #777;
    border-radius: 3px;
    transition: background 0.2s ease;
}

.status-hosts-popover::-webkit-scrollbar-thumb:hover {
    background: #777;
}

.status-hosts-popover::-webkit-scrollbar-thumb:active {
    background: #888;
}

.status-hosts-popover h4 {
    margin: 0 0 8px 0;
    padding-bottom: 6px;
    border-bottom: 1px solid #555; 
    font-size: 13px;
    color: #ffffff;
    text-align: center;
    padding-right: 20px; /* Make room for close button */
}

.status-hosts-popover ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

.status-hosts-popover li {
    padding: 8px 10px;
    border-radius: 3px;
    cursor: pointer;
    transition: background-color 0.15s ease, color 0.15s ease;
    font-size: 12px;
    color: #e0e0e0;
    margin-bottom: 2px;
    border-bottom: 1px solid rgba(255,255,255,0.05);
}

.status-hosts-popover li:last-child {
    border-bottom: none;
}

.status-hosts-popover li:hover {
    background-color: rgba(255,255,255,0.1);
    color: #ffffff;
}

.status-hosts-popover .close-popover {
    position: absolute;
    top: 6px;
    right: 8px;
    cursor: pointer;
    color: #aaa;
    font-size: 16px;
    width: 20px;
    height: 20px;
    text-align: center;
    line-height: 20px;
    border-radius: 50%;
}

.status-hosts-popover .close-popover:hover {
    color: #fff;
    background-color: rgba(255,255,255,0.1);
}

/* Mobile Status Container Styles */
.mobile-status-container {
    display: none; /* Hidden by default, shown via JS */
    flex-direction: column;
    background-color: #403c3c;
    padding: 8px 15px;
    border-bottom: 1px solid #555;
    position: relative;
    z-index: 8;
}

    /* Only show the mobile status container in mobile view */
@media (max-width: 768px) {
    /* Move things from header to mobile container */
    .mobile-status-container {
        display: flex;
        gap: 10px;
        align-items: center;
        justify-content: center;
        text-align: center;
    }
    
    /* Ensure refresh icon is visible on mobile */
    .refresh-countdown i.fa-refresh {
        display: inline-block !important;
        visibility: visible !important;
        color: #dddddd !important;
        opacity: 1 !important;
    }
    
    /* Adjust status filters in mobile container */
    .mobile-status-container .hostlist-status-filters-header {
        margin-left: 0;
        flex-direction: column;
        width: 100%;
        align-items: center;
        justify-content: center;
    }
    
    .mobile-status-container .hostlist-filter-section-header {
        width: 100%;
        justify-content: center;
        margin-right: 0;
        margin-bottom: 5px;
    }
    
    /* Adjust status indicators in mobile container */
    .mobile-status-container .hostlist-status-indicators {
        margin-left: 0;
        margin-top: 5px;
        width: 100%;
        justify-content: center;
    }
    
    /* Make sure header doesn't show these elements on mobile */
    header .hostlist-status-filters-header,
    header .hostlist-status-indicators {
        display: none;
    }
}

/* Context Menu Styles */
.commands-card {
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    padding: 8px;
    max-width: 600px;
    border: 1px solid #e0e0e0;
    opacity: 0.95;
}

.card-title {
    font-size: 14px;
    font-weight: 600;
    padding-bottom: 6px;
    margin-top: 5px;
    border-bottom: 1px solid #eee;
    margin-bottom: 6px;
    color: #333;
}

.command-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.command-link {
    text-decoration: none;
    color: #333;
    font-size: 13px;
    display: block;
}

.command-item {
    display: flex;
    align-items: center;
    padding: 4px 6px;
    border-radius: 4px;
    transition: all .1s ease-out;
}

.command-item:hover {
    background: #f0f0f0;
    transform: translateX(2px);
}

.command-icon {
    margin-right: 6px;
    color: #777;
    font-size: 13px;
}

/* Modal Styles for Commands */
#service-modal.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(4px);
    z-index: 1000;
    align-items: center;
    justify-content: center;
    opacity: 0.98;
}

#service-modal .modal-content {
    background: #f4f4f4;
    border-radius: 16px;
    width: 87%;
    max-width: 700px;
    padding: 24px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
    border: 1px solid #e0e0e0;
    position: relative;
    color: #333;
}

#service-modal .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 18px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e0e0e0;
}

#service-modal .modal-title {
    font-size: 22px;
    font-weight: 700;
    margin: 0;
    color: #333;
    letter-spacing: -0.025em;
}

#service-modal .modal-close {
    cursor: pointer;
    color: #777;
    font-size: 14px;
    padding: 3px;
    transition: all 0.2s ease;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 50%;
    width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 12px;
    right: 12px;
}

#service-modal .modal-close:hover {
    color: #333;
    background: rgba(0, 0, 0, 0.1);
    transform: scale(1.05);
}

#service-modal .modal-body {
    font-size: 14px;
    color: #333;
    line-height: 1;
    overflow-y: auto;
    max-height: 62vh;
    will-change: auto;
    backface-visibility: hidden;
    transform: translateZ(0);
    scroll-behavior: smooth;
}

#service-modal .modal-options {
    cursor: pointer;
    color: #777;
    font-size: 14px;
    padding: 3px;
    transition: all 0.2s ease;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 50%;
    width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 12px;
    right: 42px;
}

#service-modal .modal-delete {
    cursor: pointer;
    color: #777;
    font-size: 14px;
    padding: 3px;
    transition: all 0.2s ease;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 50%;
    width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 12px;
    right: 74px;
}

#service-modal .modal-options:hover,
#service-modal .modal-delete:hover {
    color: #333;
    background: rgba(0, 0, 0, 0.1);
    transform: scale(1.05);
}

/* Modal scrollbar styling */
#service-modal .modal-body::-webkit-scrollbar {
    width: 6px;
}

#service-modal .modal-body::-webkit-scrollbar-track {
    background: transparent;
    margin: 4px 0;
}

#service-modal .modal-body::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    border: 1px solid #f4f4f4;
}

#service-modal .modal-body::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}

/* Pagination Controls */
.hostlist-pagination {
    display: flex;
    gap: 6px;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    padding: 10px 0;
}

.hostlist-pagination .page-btn {
    background-color: #fafafa;
    border: 1px solid #e0e0e0;
    color: #333;
    padding: 4px 10px;
    font-size: 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s, color 0.2s, transform 0.1s;
}

.hostlist-pagination .page-btn:hover:not(:disabled) {
    background-color: #f0f0f0;
    transform: translateY(-1px);
}

.hostlist-pagination .page-btn:disabled {
    opacity: 0.5;
    cursor: default;
}

.hostlist-pagination .page-btn.active {
    background-color: #4a7fbe;
    color: #fff;
    border-color: #3a6fa5;
    font-weight: 600;
}

.hostlist-pagination .page-ellipsis {
    color: #999;
    font-size: 14px;
    user-select: none;
}  