/* Notifications Page - Dark Theme */

.notifications-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: #1a1a1a;
    overflow: visible;
}

/* Controls bar */
.notifications-controls {
    padding: 12px 18px;
    background-color: #252525;
    border-bottom: 1px solid #333;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.controls-row {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    align-items: center;
}

.control-group {
    display: flex;
    align-items: center;
    gap: 6px;
}

.control-group label {
    color: #ffffff;
    font-size: 14px;
    white-space: nowrap;
}

.control-group input[type="datetime-local"],
.control-group input[type="text"],
.control-group select {
    background-color: #2c2c2c;
    border: none;
    border-radius: 4px;
    color: #e0e0e0;
    padding: 6px 10px;
    font-size: 14px;
    height: 32px;
}

/* Notification type filter specific styling */
#notification-type-filter {
    min-width: 200px;
}

#notification-type-filter optgroup {
    font-weight: 600;
    color: #aaa;
    background-color: #1a1a1a;
}

#notification-type-filter option {
    color: #e0e0e0;
    background-color: #2c2c2c;
    padding: 4px 8px;
}

/* Scrollbar styling for the dropdown */
#notification-type-filter::-webkit-scrollbar {
    width: 8px;
}

#notification-type-filter::-webkit-scrollbar-track {
    background: #1a1a1a;
}

#notification-type-filter::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 4px;
}

#notification-type-filter::-webkit-scrollbar-thumb:hover {
    background: #777;
}

/* Calendar picker icon styling for dark theme */
.control-group input[type="datetime-local"]::-webkit-calendar-picker-indicator {
    filter: invert(1) brightness(1.5);
    opacity: 0.9;
    cursor: pointer;
}

.control-group input[type="datetime-local"]::-moz-calendar-picker-indicator {
    filter: invert(1) brightness(1.8);
    opacity: 0.9;
    cursor: pointer;
}

/* Search input specific styling */
.search-input-wrapper {
    position: relative;
    flex: 1;
    display: flex;
    align-items: center;
}

#notification-search {
    min-width: 250px;
    background-color: #2c2c2c;
    border: none;
    border-radius: 4px;
    color: #e0e0e0;
    padding: 8px 40px 8px 15px;
    font-size: 14px;
    height: 22px;
    flex: 1;
}

#notification-search:focus {
    outline: none;
    background-color: #333;
}

#notification-search::placeholder {
    color: #999;
}

.search-actions {
    display: flex;
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    align-items: center;
}

.notification-clear-search {
    background: transparent;
    border: none;
    color: #999;
    font-size: 16px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
}

.notification-clear-search:hover {
    color: #fff;
}

.notification-clear-search.visible {
    opacity: 1;
    visibility: visible;
}

.generate-btn {
    background: transparent;
    border: 1px solid #555;
    color: #aaa;
    border-radius: 4px;
    padding: 6px 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.generate-btn:hover {
    background-color: #444;
    color: #fff;
}

/* Vertical separator */
.vertical-separator {
    width: 1px;
    height: 32px;
    background-color: #555;
    margin: 0 8px;
    flex-shrink: 0;
}

/* Content */
.notifications-content {
    flex: 1;
    overflow-y: auto;
    padding: 0 18px 18px;
    background-color: #1a1a1a;
    margin-bottom: 20px;
    max-height: calc(100vh - 200px);
}

.notifications-loading,
.notifications-empty,
.notifications-error,
.notifications-placeholder {
    text-align: center;
    color: #aaa;
    font-size: 16px;
    padding: 40px 20px;
}

.notifications-error {
    color: #ff6b6b;
}

/* Table */
.notification-table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    font-size: 14px;
}

.notification-table thead {
    position: sticky;
    top: -1px;
    background-color: #2c2c2c;
    z-index: 5;
    padding-top: 1px;
}

.notification-table th,
.notification-table td {
    padding: 10px 14px;
    text-align: left;
    vertical-align: middle;
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #e0e0e0;
}

.notification-table tbody tr:nth-child(even) {
    background-color: rgba(255, 255, 255, 0.02);
}

.notification-table tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* Status colours */
.ok { color: #4CAF50; }
.warning { color: #FFC107; }
.critical { color: #F44336; }
.unknown { color: #64748b; }
.down { color: #F44336; }
.unreachable { color: #64748b; }

/* Notification Summary */
.notification-summary {
    background: linear-gradient(135deg, #2c2c2c 0%, #252525 100%);
    border: 1px solid #333;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

.summary-title {
    margin: 0 0 15px 0;
    font-size: 20px;
    font-weight: 600;
    color: #e0e0e0;
    text-align: center;
}

.summary-time-range {
    margin: 0 0 15px 0;
    color: #aaa;
    font-size: 14px;
    text-align: center;
    font-style: italic;
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.summary-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #1a1a1a;
    border-radius: 6px;
    border: 1px solid #333;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
    transition: all 0.2s ease;
}

.summary-stat:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(0,0,0,0.3);
}

.summary-stat.warning {
    border-left: 4px solid #FFC107;
    background: linear-gradient(135deg, #2c2c2c 0%, #252525 100%);
}

.summary-stat.critical {
    border-left: 4px solid #F44336;
    background: linear-gradient(135deg, #2c2c2c 0%, #252525 100%);
}

.summary-stat.unknown {
    border-left: 4px solid #64748b;
    background: linear-gradient(135deg, #2c2c2c 0%, #252525 100%);
}

.summary-stat.down {
    border-left: 4px solid #F44336;
    background: linear-gradient(135deg, #2c2c2c 0%, #252525 100%);
}

.summary-stat.unreachable {
    border-left: 4px solid #64748b;
    background: linear-gradient(135deg, #2c2c2c 0%, #252525 100%);
}

.stat-label {
    font-size: 14px;
    font-weight: 500;
    color: #aaa;
}

.stat-value {
    font-size: 16px;
    font-weight: 600;
    color: #e0e0e0;
}

.summary-stat.warning .stat-value {
    color: #ffb74d;
}

.summary-stat.critical .stat-value,
.summary-stat.down .stat-value {
    color: #ef5350;
}

.summary-stat.unknown .stat-value,
.summary-stat.unreachable .stat-value {
    color: #90a4ae;
}

/* Status filters */
.notifications-status-filters {
    display: flex;
    gap: 8px;
    align-items: center;
}

.notification-status-filter {
    padding: 6px 12px;
    border: 1px solid #555;
    background: #2c2c2c;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;
    color: #e0e0e0;
}

.notification-status-filter:hover {
    background-color: #333;
}

.notification-status-filter.active {
    color: #fff;
    border-color: transparent;
}

.notification-status-filter.ok {
    color: #4CAF50;
    border-color: #4CAF50;
}

.notification-status-filter.ok.active {
    background-color: #4CAF50;
}

.notification-status-filter.warning {
    color: #FFC107;
    border-color: #FFC107;
}

.notification-status-filter.warning.active {
    background-color: #FFC107;
}

.notification-status-filter.critical {
    color: #F44336;
    border-color: #F44336;
}

.notification-status-filter.critical.active {
    background-color: #F44336;
}

.notification-status-filter.unknown {
    color: #64748b;
    border-color: #64748b;
}

.notification-status-filter.unknown.active {
    background-color: #64748b;
}

.notification-status-filter.down {
    color: #F44336;
    border-color: #F44336;
}

.notification-status-filter.down.active {
    background-color: #F44336;
}

.notification-status-filter.unreachable {
    color: #64748b;
    border-color: #64748b;
}

.notification-status-filter.unreachable.active {
    background-color: #64748b;
}

/* Notification type indicators */
.notification-type-host {
    background-color: #1e3a5f;
    color: #64b5f6;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 500;
}

.notification-type-service {
    background-color: #4a148c;
    color: #ba68c8;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 500;
}

/* Notification type status colors */
.notification-type-recovery {
    background-color: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 500;
}

.notification-type-critical,
.notification-type-down {
    background-color: rgba(244, 67, 54, 0.2);
    color: #F44336;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 500;
}

.notification-type-warning {
    background-color: rgba(255, 193, 7, 0.2);
    color: #FFC107;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 500;
}

/* Message formatting */
.notification-message {
    max-width: 400px;
    word-wrap: break-word;
    line-height: 1.4;
}

.notification-message.critical {
    color: #ef5350;
    font-weight: 500;
}

.notification-message.warning {
    color: #ffb74d;
    font-weight: 500;
}

.notification-message.ok {
    color: #66bb6a;
    font-weight: 500;
}

/* Timestamp formatting */
.notification-timestamp {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    color: #aaa;
}

/* Contact formatting */
.notification-contact {
    font-weight: 500;
    color: #64b5f6;
}

/* Clickable host and service cells */
.clickable-host,
.clickable-service {
    cursor: pointer;
    color: #64b5f6;
    text-decoration: underline;
    transition: all 0.2s ease;
    position: relative;
}

.clickable-host:hover,
.clickable-service:hover {
    color: #90caf9;
    text-decoration: underline;
    background-color: rgba(100, 181, 246, 0.1);
}

.clickable-host:active,
.clickable-service:active {
    color: #42a5f5;
    transform: translateY(1px);
}

/* Method formatting */
.notification-method {
    font-size: 12px;
    color: #aaa;
    font-style: italic;
}

/* Responsive design */
@media (max-width: 768px) {
    .controls-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .control-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    .control-group label {
        margin-bottom: 4px;
    }
    
    .search-input-wrapper {
        width: 100%;
    }
    
    #notification-search {
        min-width: auto;
        width: 100%;
    }
    
    .notifications-status-filters {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .notification-table {
        font-size: 12px;
    }
    
    .notification-table th,
    .notification-table td {
        padding: 8px 10px;
    }
    
    .notification-message {
        max-width: 200px;
    }
}

/* Loading spinner */
.notifications-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #333;
    border-top: 4px solid #64b5f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Empty state */
.notifications-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    color: #666;
}

.notifications-empty i {
    font-size: 48px;
    opacity: 0.5;
}

.empty-time-range {
    color: #aaa;
    font-size: 14px;
    font-style: italic;
    margin-top: 5px;
}

/* Error state */
.notifications-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    color: #ff6b6b;
}

.notifications-error i {
    font-size: 48px;
    opacity: 0.7;
}

/* Filters Modal Styles */
#filtersModal .sr-modal-content {
    background: #2a2a2a;
    color: #e0e0e0;
}

#filtersModal .sr-field label {
    color: #e0e0e0;
    font-weight: 500;
}

#filtersModal .sr-field input,
#filtersModal .sr-field select {
    background: #333;
    border: 1px solid #555;
    color: #e0e0e0;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

#filtersModal .sr-field input:focus,
#filtersModal .sr-field select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.2);
}

/* Calendar picker icon styling for filters modal */
#filtersModal input[type="datetime-local"]::-webkit-calendar-picker-indicator {
    filter: invert(1) brightness(1.5);
    opacity: 0.9;
    cursor: pointer;
}

#filtersModal input[type="datetime-local"]::-moz-calendar-picker-indicator {
    filter: invert(1) brightness(1.8);
    opacity: 0.9;
    cursor: pointer;
}

/* Time shortcuts styling */
.time-shortcuts {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 5px;
}

.time-shortcut-btn {
    background: #333;
    border: 1px solid #555;
    color: #e0e0e0;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.time-shortcut-btn:hover {
    background: #444;
    border-color: #666;
    color: #fff;
}

.time-shortcut-btn.active {
    background: #007bff;
    border-color: #007bff;
    color: white;
}

/* Schedule Report Modal */
.sr-modal{display:none;position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.6);z-index:1000;align-items:center;justify-content:center;}
.sr-modal-content{background:#252525;padding:25px 30px;border-radius:8px;min-width:320px;max-width:1200px;max-height:90vh;color:#fff;overflow-y:auto;}
.sr-modal-content h2{margin-top:0;margin-bottom:15px;font-size:20px;}
.sr-close{float:right;font-size:28px;cursor:pointer;color:#aaa;}
.sr-close:hover{color:#fff;}
.sr-field{display:flex;flex-direction:column;gap:6px;margin-bottom:15px;}
.sr-field label{font-size:14px;color:#e0e0e0;font-weight:500;}
.sr-field input, .sr-field select{background:#2c2c2c;border:1px solid #444;color:#fff;padding:8px 12px;border-radius:4px;font-size:14px;transition:border-color 0.2s ease;}
.sr-field input:focus, .sr-field select:focus{outline:none;border-color:#007bff;box-shadow:0 0 0 2px rgba(0,123,255,0.2);}
.sr-actions{display:flex;justify-content:flex-end;gap:10px;margin-top:20px;padding-top:15px;border-top:1px solid #444;}

/* Buttons inside modal - matching settings modal styles */
.sr-modal .generate-btn {
    background: #444;
    background-color: #444;
    color: #f8f9fa;
    padding: 6px 10px;
    min-width: 32px;
    height: 32px;
    border: 1px solid #888;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    line-height: 1;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.sr-modal .generate-btn:hover {
    background-color: #555;
    border: 1px solid #888;
    border-radius: 4px;
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.form-row .sr-field {
    flex: 1;
}
