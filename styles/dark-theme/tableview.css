/* Table View Styles - Dark Theme */

/* Minor layout tweaks specific to table view sections */
html, body { 
    height: 100%; 
}

body { 
    margin: 0; 
    display: flex; 
    flex-direction: column; 
    min-height: 100vh; 
    overflow: hidden; 
}

.tableview-sections {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: 20px;
    align-items: start;
    padding: 16px;
    max-width: 100%;
}

.tableview-section {
    background: #1e1e1e;
    border-radius: 6px;
    box-shadow: var(--card-shadow, 0 1px 3px rgba(0,0,0,0.3));
    overflow: hidden;
    min-width: 0;
    max-width: 100%;
    border: 1px solid #333;
}

.tableview-section h3 {
    margin: 0;
    padding: 8px 12px;
    background: #252525;
    border-bottom: 1px solid #333;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 16px;
    color: #ffffff;
}

.tableview-section .hostlist-table { 
    margin: 0; 
    table-layout: fixed; 
    width: 100%; 
    border-collapse: collapse;
}

.tableview-section .hostlist-table th,
.tableview-section .hostlist-table td {
    padding: 6px 8px;
    text-align: left;
    vertical-align: middle;
    word-wrap: break-word;
    overflow-wrap: break-word;
    font-size: 13px;
}

/* Center status and services columns */
.tableview-section .hostlist-table .col-status,
.tableview-section .hostlist-table .col-service {
    text-align: center;
}

.tableview-section .hostlist-table th {
    background: #252525;
    font-weight: 600;
    border-bottom: 1px solid #333;
    font-size: 12px;
    color: #ffffff;
}

.tableview-section .hostlist-table td {
    color: #e0e0e0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
}

.svc-badges {
    display: inline-flex; 
    gap: 4px; 
    align-items: center; 
    flex-wrap: wrap;
    margin: 1px 0;
}

.svc-badge { 
    padding: 2px 6px; 
    border-radius: 8px; 
    font-size: 10px; 
    font-weight: 500;
    white-space: nowrap;
}

.svc-badge.ok { 
    background: rgba(205, 224, 107, 0.2); 
    color: #cde06b; 
}

.svc-badge.warning { 
    background: rgba(255, 165, 0, 0.2); 
    color: #ffa500; 
}

.svc-badge.critical { 
    background: rgba(212, 29, 40, 0.2); 
    color: #d41d28; 
}

.svc-badge.unknown { 
    background: rgba(100, 116, 139, 0.2); 
    color: #64748b; 
}

.tableview-empty { 
    padding: 24px; 
    text-align: center; 
    opacity: 0.7; 
    color: #aaaaaa;
}

.hostlist-status { 
    display: inline-block; 
    white-space: nowrap; 
    max-width: none; 
    padding: 3px 6px;
    border-radius: 4px;
    font-weight: 500;
    font-size: 12px;
}

.hostlist-container { 
    flex: 1; 
    overflow: auto; 
    padding: 0; 
    margin: 0;
    background-color: #1a1a1a;
    display: flex;
    flex-direction: column;
}

.tableview-content { 
    padding: 4px; 
    margin: 0;
    overflow-x: auto;
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* Loading indicator centering for tableview */
.tableview-content .hostlist-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 400px;
    color: #aaaaaa;
    gap: 20px;
}

/* Custom scrollbar for dark theme */
.tableview-content::-webkit-scrollbar {
    width: 8px;
}

.tableview-content::-webkit-scrollbar-track {
    background: #2a2a2a;
    border-radius: 4px;
}

.tableview-content::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 4px;
    transition: background 0.2s ease;
}

.tableview-content::-webkit-scrollbar-thumb:hover {
    background: #777;
}

.tableview-content::-webkit-scrollbar-thumb:active {
    background: #777;
}

/* Host cell content styling */
.host-cell-content {
    display: flex;
    align-items: center;
    gap: 4px;
    min-width: 0;
    overflow: hidden;
}

.host-cell-content .hostname {
    flex: 1;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 13px;
    color: #e0e0e0;
}

/* Only highlight hostname when down */
.host-cell-content .hostname.down {
    color: #d41d28;
    font-weight: 600;
}

.host-cell-content .graph-icon {
    flex-shrink: 0;
    margin-right: 2px;
    font-size: 12px;
    color: #999;
}

.host-cell-content .graph-icon:hover {
    color: #4a7fbe;
}

/* Status column styling */
.col-status .hostlist-status {
    display: inline-block;
    padding: 3px 6px;
    border-radius: 4px;
    font-weight: 500;
    text-align: center;
    min-width: 50px;
    font-size: 12px;
}

/* Service badges container */
.col-service .svc-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 3px;
    align-items: center;
}

/* Table row hover effects for dark theme */
.tableview-section .hostlist-table .hostlist-host-row:hover {
    background-color: rgba(255, 255, 255, 0.08) !important;
}

.tableview-section .hostlist-table .hostlist-host-row[data-status="down"] {
    background-color: rgba(212, 29, 40, 0.15);
}

.tableview-section .hostlist-table .hostlist-host-row[data-status="down"]:hover {
    background-color: rgba(212, 29, 40, 0.2) !important;
}

.tableview-section .hostlist-table .hostlist-host-row[data-status="unknown"] {
    background-color: rgba(100, 116, 139, 0.15);
}

.tableview-section .hostlist-table .hostlist-host-row[data-status="unknown"]:hover {
    background-color: rgba(100, 116, 139, 0.2) !important;
}

/* Status colors for dark theme */
.hostlist-status.ok {
    background-color: rgba(205, 224, 107, 0.2);
    color: #cde06b;
}

.hostlist-status.down {
    background-color: rgba(212, 29, 40, 0.2);
    color: #d41d28;
}

.hostlist-status.unknown {
    background-color: rgba(100, 116, 139, 0.2);
    color: #64748b;
}

.hostlist-status.warning {
    background-color: rgba(255, 165, 0, 0.2);
    color: #ffa500;
}

.hostlist-status.critical {
    background-color: rgba(212, 29, 40, 0.2);
    color: #d41d28;
}

.hostlist-status.pending {
    background-color: rgba(128, 128, 128, 0.2);
    color: #808080;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .tableview-sections {
        grid-template-columns: 1fr;
        gap: 12px;
        padding: 8px;
    }
    .tableview-section .hostlist-table th,
    .tableview-section .hostlist-table td {
        padding: 5px 6px;
    }
}

@media (max-width: 480px) {
    .tableview-sections {
        padding: 6px;
    }
    .tableview-section h3 {
        padding: 8px 10px;
        font-size: 13px;
    }
}
