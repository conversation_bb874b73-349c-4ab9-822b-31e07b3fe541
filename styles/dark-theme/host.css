:root {
    --primary: #888;
    --pending: #808282;
    --success: #b4ca45;
    --warning: #fec930;
    --critical: #d41d28;
    --unknown: #4a7fbe;
    --surface: #2a2a2a;
    --surface-hover: #3a3a3a;
    --header-colour: #1a1a1a;
    --background: #2a2a2a;
    --text: #ffffff;
    --text-secondary: #cccccc;
    --border: #444444;
    --radius: 16px;
    --shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    --shadow-hover: 0 4px 12px rgba(0, 0, 0, 0.4);
    --pending-bg: #2a2a2a;
    --success-bg: #1a2a1a;
    --warning-bg: #2a2a1a;
    --critical-bg: #2a1a1a;
    --unknown-bg: #1a1a2a;
}

body {
    background: var(--background);
    color: var(--text);
    margin: 0;
    padding: 0;
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    line-height: 1.6;
    font-weight: 400;
    overflow-x: hidden;
}

/* Modern Header Styles */
.header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: var(--background);
    color: #fff;
    padding: 20px;
    height: 55px;
    display: flex;
    align-items: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid #717171;
    box-sizing: border-box;
    z-index: 1000;
}

.header-content {
    max-width: 1440px;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    position: relative;
}

.header-title {
    margin: 0;
    font-size: 26px;
    font-weight: 600;
    color: #fff;
    letter-spacing: -0.02em;
    display: flex;
    align-items: center;
    text-align: center;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 20px;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
}

/* Primary Actions */
.primary-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* Tools Dropdown Container */
.tools-dropdown {
    position: relative;
    display: inline-flex;
    align-items: center;
}

/* Tools Button */
.tools-button {
    background: none;
    border: none;
    color: var(--text);
    font-size: 18px;
    padding: 8px;
    cursor: pointer;
    border-radius: 50%;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
}

.tools-button:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.05);
}

/* Tools Dropdown Menu */
.tools-menu {
    display: none;
    position: absolute;
    top: calc(100% + 8px);
    right: 0;
    background-color: var(--surface);
    min-width: 250px;
    border-radius: 8px;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
    z-index: 1001;
    padding: 8px 0;
    opacity: 0;
    transform: translateY(-10px);
    transition: opacity 0.2s ease, transform 0.2s ease;
    border: 1px solid var(--border);
}

/* Show the tools menu when active */
.tools-menu.active {
    display: block;
    opacity: 1;
    transform: translateY(0);
    opacity: 0.95;
}

/* Tools Menu Items */
.tools-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    color: var(--text);
    font-size: 14px;
    font-weight: 400;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.tools-item:hover {
    background-color: var(--surface-hover);
}

.tools-item i {
    font-size: 16px;
    width: 16px;
    text-align: center;
    color: var(--text);
}

.tools-item img {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
    filter: brightness(0) invert(1);
}

.tools-item span {
    flex: 1;
}

/* OS Dropdown Container */
.os-dropdown {
    position: relative;
    display: inline-flex;
    align-items: center;
}

/* OS Icon (Main Icon in Header) */
.os-icon {
    width: 20px;
    height: 20px;
    cursor: pointer;
    padding: 6px;
    transition: transform 0.2s ease;
    display: block;
}

.os-icon:hover {
    transform: scale(1.1);
}

/* OS Dropdown Menu */
.os-menu {
    display: none;
    position: absolute;
    top: calc(100% + 8px);
    right: 0;
    background-color: #444;
    min-width: 160px;
    max-height: 300px;
    overflow: auto;
    border-radius: 8px;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
    z-index: 1001;
    padding: 8px 0;
    opacity: 0;
    transform: translateY(-10px);
    transition: opacity 0.2s ease, transform 0.2s ease;
    border: 1px solid #717171;
}

/* Show the menu when active */
.os-menu.active {
    display: block;
    opacity: 1;
    transform: translateY(0);
    opacity: 0.95;
}

.os-menu::-webkit-scrollbar {
    width: 6px;
}

.os-menu::-webkit-scrollbar-track {
    background: transparent;
    margin: 4px 0;
}

.os-menu::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    border: 1px solid #444;
}

.os-menu::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}

/* OS Menu Items */
.os-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 16px;
    color: #fff;
    font-size: 14px;
    font-weight: 400;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.os-item:hover {
    background-color: #555;
}

/* SVG Icons inside Menu Items */
.os-item img {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
}

.os-icon, .os-item img {
    object-fit: contain;
}

/* Refresh Icon */
.header-icons {
    cursor: pointer;
    color: var(--text);
    font-size: 18px;
    padding: 8px;
    transition: color 0.2s ease, transform 0.2s ease;
    line-height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: none;
    border: none;
}

.header-icons:hover {
    color: #ddd;
    transform: rotate(90deg);
}

/* Content Wrapper */
.content-wrapper {
    margin-top: 55px;
    padding: 32px 40px; /* Increased horizontal padding to use more width */
    min-height: calc(100vh - 55px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .header {
        padding: 12px 16px;
        height: 55px;
    }

    .header-title {
        font-size: 20px;
    }

    .header-actions {
        gap: 16px;
    }

    .primary-actions {
        gap: 8px;
    }

    .tools-button {
        width: 32px;
        height: 32px;
        font-size: 16px;
        padding: 6px;
    }

    .tools-menu {
        min-width: 180px;
        right: -8px;
    }

    .tools-item {
        padding: 10px 14px;
        font-size: 13px;
    }

    .os-icon {
        width: 18px;
        height: 18px;
        padding: 4px;
    }

    .header-icons {
        font-size: 18px;
        padding: 4px;
        line-height: 18px;
    }

    .content-wrapper {
        margin-top: 55px;
        padding: 20px 24px; /* Increased horizontal padding for mobile */
    }
    
    .os-menu {
        min-width: 140px;
    }

    .os-item {
        font-size: 13px;
        padding: 8px 12px;
    }

    .os-item img {
        width: 14px;
        height: 14px;
    }

    /* Single column layout for mobile */
    .container {
        grid-template-columns: 1fr;
    }

    .status-header {
        grid-column: 1;
        display: block;
    }
    
    .status-badge {
        margin-bottom: 8px;
        grid-column: 1;
    }

    /* Force host availability container to full width */
    .host-availability-container {
        grid-column: 1;
        width: 100%;
        margin-bottom: 16px; /* Reduced space since host card is now at top */
        flex-direction: column; /* Stack vertically on mobile */
    }
    
    .host-card {
        width: 100%; /* Full width on mobile */
        margin-bottom: 16px; /* Add space between host card and availability graph */
    }
    
    .spm-marketing-container {
        min-width: 100%;
        margin-top: 16px;
    }

    /* Force services column to full width */
    .services-column {
        grid-column: 1;
        width: 100%;
    }

    .services-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); /* Increased from 220px for better mobile layout */
    }

    .modal-grid {
        grid-template-columns: 1fr;
        display: flex;
        flex-direction: column;
        gap: 8px;
    }
    
    /* Responsive Tab Navigation */
    .tab-navigation {
        padding-bottom: 5px;
    }
    
    .tab-button {
        padding: 8px 12px;
        font-size: 13px;
        margin-right: 4px;
    }
    
    .modal-content {
        width: 95%;
        padding: 20px 16px;
    }
    
    .modal-body {
        max-height: 70vh;
    }

    .service-header {
        padding-right: 0;
    }

    .service-icon-container {
        right: 36px;
    }

    .service-options {
        right: 12px;
    }

    .comments-table {
        font-size: 11px;
        margin-bottom: 10px;
    }

    .comments-table th,
    .comments-table td {
        padding: 4px 6px;
    }

    .comments-table thead {
        display: none;
    }

    .comments-table tbody,
    .comments-table tr,
    .comments-table td {
        display: block;
        width: 100%;
    }

    .comments-table tr {
        margin-bottom: 8px;
        border-bottom: none;
    }

    .comments-table td {
        position: relative;
        padding-left: 40%;
        text-align: left;
    }

    .comments-table td:before {
        content: attr(data-label);
        position: absolute;
        left: 6px;
        width: 35%;
        padding-right: 10px;
        font-weight: 600;
        text-transform: uppercase;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    #host-comment-grid p {
        font-size: 11px;
        margin: 4px 0 0 0;
    }

    .error {
        padding: 6px;
        margin-bottom: 10px;
        font-size: 11px;
    }
}

@media (max-width: 480px) {
    .comments-table {
        font-size: 10px;
    }

    .comments-table td {
        padding-left: 50%;
    }

    .comments-table td:before {
        width: 45%;
    }
    
    /* Smaller screens Tab Navigation */
    .tab-button {
        padding: 6px 10px;
        font-size: 12px;
        margin-right: 2px;
    }
    
    .tab-navigation {
        margin-bottom: 15px;
    }
}

/* Rest of your existing styles */
.container {
    max-width: 1800px; /* Increased from 1440px */
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr; /* Single column layout */
    gap: 24px;
}

.status-header {
    grid-column: 1 / -1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    flex-wrap: nowrap;
}

.status-badge {
    padding: 0;
    background: transparent;
    border-radius: 0;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-secondary);
    box-shadow: none;
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 4px;
}

.host-column {
    display: flex;
    flex-direction: column;
    gap: 24px;
    grid-column: 1;
    margin-bottom: 24px; /* Add space between host card and content below */
}

.host-availability-container {
    display: flex;
    flex-direction: row;
    gap: 24px;
    grid-column: 1;
    margin-bottom: 24px;
    background: var(--surface);
    border-radius: var(--radius);
    padding: 24px;
    box-shadow: var(--shadow);
    border: 1px solid var(--border);
    align-items: flex-start;
    flex-wrap: wrap;
}

.host-card {
    background: var(--surface);
    border-radius: var(--radius);
    padding: 20px;
    box-shadow: var(--shadow);
    border-left: 6px solid;
    transition: all 0.3s ease;
    cursor: pointer;
    margin-top: 0;
    position: relative;
    border: 1px solid #717171;
    width: 300px; /* Fixed width for the host card */
    flex-shrink: 0; /* Prevent shrinking */
    margin-bottom: 0; /* Remove margin since it's now horizontal */
}

.host-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.host-status {
    display: flex;
    align-items: center;
    gap: 14px;
    margin-bottom: 12px;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
}

.host-name {
    font-weight: 700;
    font-size: 18px;
    color: var(--text);
}

.host-details {
    font-size: 15px;
    color: var(--text-secondary);
    margin-top: 8px;
}

.host-mac-addresses {
    margin-top: 8px;
}

.mac-addresses-section {
    padding-top: 8px;
    margin-top: 8px;
    border-top: 1px solid var(--border);
}

.mac-addresses-section h4 {
    margin: 0 0 8px 0;
    font-size: 13px;
    color: var(--text-secondary);
    font-weight: 600;
}

.mac-address-item {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    font-size: 12px;
    line-height: 1.3;
}

.mac-address-item i {
    width: 16px;
    margin-right: 6px;
    color: var(--text-secondary);
    font-size: 11px;
}

.mac-label {
    font-weight: 600;
    color: var(--text-secondary);
    margin-right: 6px;
    min-width: 70px;
}

.mac-value {
    font-family: 'Courier New', monospace;
    color: var(--text);
    font-weight: 500;
    background: var(--surface);
    padding: 2px 6px;
    border-radius: 3px;
    border: 1px solid var(--border);
}

.host-icon {
    position: absolute;
    top: 12px;
    font-size: 16px;
    color: var(--critical);
    opacity: 0.8;
    transition: opacity 0.2s ease;
}

.host-card:hover .host-icon {
    opacity: 1;
}

.host-options {
    position: absolute;
    top: 12px;
    right: 12px;
    cursor: pointer;
    padding: 0 5px;
    z-index: 2;
}

.host-options i {
    font-size: 16px;
    color: var(--text);
    opacity: 0.8;
    transition: opacity 0.2s ease, color 0.2s ease;
}

.host-card:hover .host-options i {
    opacity: 1;
}

.host-card .host-icon:nth-child(2) { right: 36px; }
.host-card .host-icon:nth-child(3) { right: 56px; }
.host-card .host-icon:nth-child(4) { right: 76px; }
.host-card .host-icon:nth-child(5) { right: 96px; }
.host-card .host-icon:nth-child(6) { right: 116px; }
.host-card .host-icon:nth-child(7) { right: 136px; }
.host-card .host-icon:nth-child(8) { right: 156px; }

/* PDF Export Modal Styles */
.pdf-export-buttons {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.pdf-export-btn-secondary {
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.2s ease;
    border: none;
    background-color: var(--surface);
    color: var(--text);
    border: 1px solid var(--border);
}

.pdf-export-btn-secondary:hover {
    background-color: var(--background);
}

.pdf-export-btn-primary {
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.2s ease;
    border: none;
    background-color: #333;
    color: var(--text);
    border: 1px solid #555;
    display: flex;
    align-items: center;
    gap: 8px;
}

.pdf-export-btn-primary:hover {
    box-shadow: var(--shadow);
    transform: translateY(-1px);
}

.pdf-export-btn-primary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.pdf-export-btn-primary i {
    font-size: 16px;
}

.services-column {
    display: flex;
    flex-direction: column;
    gap: 24px;
    grid-column: 1;
    width: 100%; /* Ensure full width */
}

.status-container {
    margin-top: 0;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr)); /* Increased from 280px and allows 4 columns */
    gap: 16px;
    max-width: 100%; /* Ensure it doesn't overflow */
}

.service-card {
    border-radius: var(--radius);
    padding: 16px;
    border-left: 6px solid;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    background: var(--surface);
    box-shadow: var(--shadow);
    cursor: pointer;
    border: 1px solid #717171;
}

.service-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-hover);
}

.service-icon-container {
    position: absolute;
    top: 12px;
    right: 36px;
    display: grid;
    grid-auto-flow: column;
    justify-content: end;
    gap: 8px;
    z-index: 1;
}

.service-icon {
    font-size: 16px;
    color: var(--critical);
    opacity: 0.8;
    transition: opacity 0.2s ease;
}

.service-card:hover .service-icon {
    opacity: 1;
}

.service-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 12px;
}

.service-title {
    font-weight: 700;
    font-size: 16px;
    color: var(--text);
}

.service-details {
    font-size: 13px;
    color: var(--text-secondary);
    line-height: 1.5;
}

.service-options {
    position: absolute;
    top: 12px;
    right: 12px;
    cursor: pointer;
    padding: 0 5px;
    z-index: 2;
}

.service-options i {
    font-size: 16px;
    color: var(--text);
    opacity: 0.8;
    transition: opacity 0.2s ease, color 0.2s ease;
}

.service-card:hover .service-options i {
    opacity: 1;
}

.pending {
    border-color: var(--pending);
    background: linear-gradient(135deg, #373737 0%, var(--pending-bg) 100%);
}

.ok {
    border-color: var(--success);
    background: linear-gradient(135deg, #3b4032 0%, var(--success-bg) 100%);
}

.warning {
    border-color: var(--warning);
    background: linear-gradient(135deg, #40382a 0%, var(--warning-bg) 100%);
}

.critical {
    border-color: var(--critical);
    background: linear-gradient(135deg, #3c2e30 0%, var(--critical-bg) 100%);
}

.unknown {
    border-color: var(--unknown);
    background: linear-gradient(135deg, #353739 0%, var(--unknown-bg) 100%);
}

.pending .status-dot {
    background: var(--pending);
}

.ok .status-dot {
    background: var(--success);
}

.warning .status-dot {
    background: var(--warning);
}

.critical .status-dot {
    background: var(--critical);
}

.unknown .status-dot {
    background: var(--unknown);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(4px);
    z-index: 1000;
    align-items: center;
    justify-content: center;
    opacity: 0.98;
}

#rename-service-modal.modal {
    display: none;
}

#rename-service-modal.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--background);
    border-radius: var(--radius);
    width: 87%;
    max-width: 700px;
    padding: 24px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
    border: 1px solid #717171;
    position: relative;
}

.modal-body::-webkit-scrollbar {
    width: 6px;
}

.modal-body::-webkit-scrollbar-track {
    background: transparent;
    margin: 4px 0;
}

.modal-body::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    border: 1px solid var(--surface);
}

.modal-body::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 18px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(113, 113, 113, 0.5);
}

.modal-title {
    font-size: 22px;
    font-weight: 700;
    margin: 0;
    color: var(--text);
    letter-spacing: -0.025em;
}

.modal-close {
    cursor: pointer;
    color: var(--text-secondary);
    font-size: 14px;
    padding: 3px;
    transition: all 0.2s ease;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 50%;
    width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 12px;
    right: 12px;
}

.modal-close:hover {
    color: var(--text);
    background: rgba(0, 0, 0, 0.1);
    transform: scale(1.05);
}

.modal-body {
    font-size: 14px;
    color: var(--text);
    line-height: 1;
    overflow-y: auto;
    max-height: 62vh;
    will-change: auto;
    backface-visibility: hidden;
    transform: translateZ(0);
    scroll-behavior: smooth;
}

/* Tab Navigation */
.tab-navigation {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 1px solid #717171;
    overflow-x: auto;
    scrollbar-width: none; /* Firefox */
}

.tab-navigation::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Edge */
}

.tab-button {
    padding: 10px 16px;
    background: transparent;
    border: none;
    border-bottom: 2px solid transparent;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 15px;
    font-weight: 500;
    transition: all 0.2s ease;
    flex-shrink: 0;
    position: relative;
    margin-right: 8px;
}

.tab-button:hover {
    color: var(--text);
}

.tab-button.active {
    color: var(--text);
    border-bottom: 2px solid var(--success);
    font-weight: 600;
}

.tab-content {
    display: none;
    position: relative;
    transition: none;
    overflow: hidden;
    will-change: auto;
    backface-visibility: hidden;
    transform: translateZ(0);
}

.tab-content.active {
    display: block;
}

.modal-row {
    margin-bottom: 18px;
    padding-bottom: 18px;
    border-bottom: 1px solid rgba(113, 113, 113, 0.5);
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.modal-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.modal-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    line-height: 1.2;
    font-size: 13px;
}

.modal-options {
    cursor: pointer;
    color: var(--text-secondary);
    font-size: 14px;
    padding: 3px;
    transition: all 0.2s ease;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 50%;
    width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 12px;
    right: 42px;
}

.modal-delete {
    cursor: pointer;
    color: var(--text-secondary);
    font-size: 14px;
    padding: 3px;
    transition: all 0.2s ease;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 50%;
    width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 12px;
    right: 74px;
}

.modal-options:hover {
    color: var(--text);
    background: rgba(0, 0, 0, 0.1);
    transform: scale(1.05);
}

.modal-delete:hover {
    color: var(--text);
    background: rgba(0, 0, 0, 0.1);
    transform: scale(1.05);
}

.loading {
    text-align: center;
    padding: 36px;
    color: var(--text-secondary);
    font-size: 15px;
    font-weight: 500;
}

/* Context Menu */
.commands-card {
    background: #333;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    padding: 8px;
    max-width: 600px;
    border: 1px solid #404040;
    opacity: 0.95;
}

.card-title {
    font-size: 14px;
    font-weight: 600;
    padding-bottom: 6px;
    margin-top: 5px;
    border-bottom: 1px solid #555;
    margin-bottom: 6px;
    color: #fff;
}

.command-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.command-link {
    text-decoration: none;
    color: #fff;
    font-size: 13px;
    display: block;
}

.command-item {
    display: flex;
    align-items: center;
    padding: 4px 6px;
    border-radius: 4px;
    transition: all .1s ease-out;
}

.command-item:hover {
    background: #444;
    transform: translateX(2px);
}

.command-icon {
    margin-right: 6px;
    color: #ccc;
    font-size: 13px;
}

/* Accordion */
.accordion {
    display: none; /* Hidden by default */
    background: var(--background);
    padding: 20px 48px 20px 24px;
    border: none;
    text-align: left;
    color: var(--text);
    width: 100%;
    position: relative;
    font-size: 17px;
    transition: all 0.3s ease;
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    border: 1px solid #717171;
    cursor: pointer;
}

.accordion-hover:hover {
    background: #333;
}

.accordion-icon {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 16px;
    color: var(--text-secondary);
}

.accordion.active i {
    transform: rotate(90deg);
    transition: transform 0.3s ease;
}

.accordion.active+#result {
    border: none;
    padding-bottom: 24px;
    box-shadow: var(--shadow);
    border-right: 1px solid #717171;
    border-bottom: 1px solid #717171;
    border-left: 1px solid #717171;
}

.accordion.active {
    border-radius: var(--radius) var(--radius) 0 0;
    box-shadow: 0 -2px 6px -1px rgba(0, 0, 0, 0.1);
    margin-bottom: 0;
}

.result {
    max-height: 0;
    display: none;
    overflow: hidden;
    transition: max-height 0.3s ease-out;
    background: var(--background);
    border-radius: 0 0 var(--radius) var(--radius);
    margin: 0 0 12px 0;
    padding: 0 24px;
}

/* Added for class-based toggle */
.result.open {
    display: block;
    visibility: visible;
    max-height: none;
    overflow: visible;
    margin-bottom: 12px;
}

.accordion-with-margin {
    margin-bottom: 12px;
}

/* When active, ensure content is visible */
.accordion.active + .result {
    display: block;
    visibility: visible;
}

/* When not active, ensure content is hidden */
.accordion:not(.active) + .result {
    max-height: 0;
    overflow: hidden;
    display: none;
}

.success {
    background-color: var(--success-bg); /* Use the success background color */
    border: 1px solid rgba(205, 224, 107, 0.3); /* Slightly opaque version of --success */
    padding: 12px 16px;
    margin: 8px 0;
    border-radius: var(--radius); /* Use the global radius variable */
    font-weight: 500;
    color: var(--success); /* Use the success color for text */
    box-shadow: var(--shadow); /* Consistent shadow with other elements */
    transition: all 0.2s ease-in-out;
}

.error {
    background-color: var(--critical-bg); /* Use the critical background color */
    border: 1px solid rgba(212, 29, 40, 0.3); /* Slightly opaque version of --critical */
    padding: 12px 16px;
    margin: 8px 0;
    border-radius: var(--radius); /* Use the global radius variable */
    font-weight: 500;
    color: var(--critical); /* Use the critical color for text */
    box-shadow: var(--shadow); /* Consistent shadow with other elements */
    transition: all 0.2s ease-in-out;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

#accordion-text.loading-tips {
    display: flex;
    align-items: center;
    gap: 8px;
}

#accordion-text.loading-tips::before {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #ccc;
    border-top-color: #333;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Nagios Comments Table Styling */
.comments-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 12px;
    font-family: 'Calibri', sans-serif;
    font-size: 12px;
    border-radius: 8px;
    overflow: hidden;
}

.comments-table thead {
    font-weight: 600;
}

.comments-table th {
    padding: 6px 8px;
    text-align: left;
    text-transform: uppercase;
}

.comments-table tbody tr {
    border-bottom: 1px solid #717171;
}

.comments-table td {
    padding: 6px 8px;
}

.comments-table a {
    text-decoration: none;
    color: #fff;
}

#host-comment-grid p {
    font-family: 'Calibri', sans-serif;
    font-size: 12px;
    margin: 6px 0 0 0;
}

iframe {
    flex: 1;
    border-radius: 12px;
    border: none;
}

/* No Performance Data Message */
.no-performance-data {
    text-align: center;
    padding: 30px 20px;
    margin: 30px auto;
    background-color: var(--surface);
    border: 1px dashed var(--border);
    border-radius: var(--radius);
    max-width: 80%;
}

.no-performance-data p {
    font-size: 16px;
    color: var(--text-secondary);
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.no-performance-data i {
    opacity: 0.7;
}

/* Desktop-only message for mobile graphs */
.desktop-only-message {
    display: none;
    text-align: center;
    padding: 30px 20px;
    margin: 30px auto;
    background-color: var(--surface);
    border: 1px dashed var(--border);
    border-radius: var(--radius);
    max-width: 100%;
}

.desktop-only-message p {
    font-size: 16px;
    color: var(--text-secondary);
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.desktop-only-message i {
    opacity: 0.7;
}

/* Tablet support - hide iframes on tablets too */
@media (max-width: 1024px) {
    .container {
        display: block !important;
    }
    
    .iframe-container {
        display: none;
    }
    
    .desktop-only-message {
        display: block !important;
    }

    .host-availability-container {
        margin-bottom: 16px; /* Reduced space since host card is now at top */
    }
    
    /* Force services column to full width */
    .services-column {
        width: 100%;
    }
}

/* Search Bar Styles */
.search-container {
    margin-bottom: 16px;
    width: 100%;
    display: none; /* Hide by default */
}

.search-container.visible {
    display: block; /* Show when services are available */
}

.search-controls {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.view-toggle-button {
    background-color: var(--surface);
    border: 1px solid var(--border);
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--text);
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.view-toggle-button:hover {
    background-color: #333;
    transform: scale(1.05);
}

.view-toggle-button i {
    font-size: 16px;
}

.search-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    background: var(--surface);
    border-radius: var(--radius);
    border: 1px solid var(--border);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: all 0.2s ease;
}

.search-wrapper:focus-within {
    box-shadow: var(--shadow-hover);
    border-color: #555;
}

.search-icon {
    position: absolute;
    left: 16px;
    color: var(--text-secondary);
    font-size: 14px;
}

.service-search {
    flex: 1;
    border: none;
    background: transparent;
    padding: 12px 42px 12px 42px;
    font-family: 'Calibri', sans-serif;
    font-size: 15px;
    color: var(--text);
    width: 100%;
    outline: none;
}

.service-search::placeholder {
    color: var(--text-secondary);
    opacity: 0.7;
}

.clear-search {
    position: absolute;
    right: 12px;
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 14px;
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
}

.clear-search.visible {
    opacity: 0.7;
    visibility: visible;
}

.clear-search:hover {
    opacity: 1;
    background: rgba(255, 255, 255, 0.1);
}

.service-card.filtered {
    display: none;
}

/* Responsive search bar */
@media (max-width: 768px) {
    .search-container {
        margin-bottom: 12px;
    }
    
    .search-controls {
        flex-direction: row;
        align-items: center;
        gap: 8px;
    }
    
    .view-toggle-button {
        align-self: flex-start;
        width: 32px;
        height: 32px;
    }
    
    .selection-mode-button {
        align-self: flex-start;
        width: 32px;
        height: 32px;
        margin-left: 0;
    }
    
    .search-wrapper {
        border-radius: calc(var(--radius) - 4px);
    }
    
    .search-icon {
        left: 12px;
        font-size: 13px;
    }
    
    .service-search {
        padding: 10px 36px 10px 36px;
        font-size: 14px;
    }
    
    .clear-search {
        right: 8px;
        font-size: 13px;
    }
    
    .filter-container {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }
    
    .service-count-badge {
        align-self: flex-start;
    }
}

/* Filter by status styles */
.filter-container {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 12px;
}

.service-count-badge {
    padding: 6px 12px;
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: 20px;
    font-size: 13px;
    font-weight: 500;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: 6px;
    flex-shrink: 0;
}

.service-count-badge i {
    font-size: 12px;
}

.filter-label {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-secondary);
}

.filter-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.filter-btn {
    background-color: var(--surface);
    border: 1px solid var(--border);
    color: var(--text);
    border-radius: 20px;
    padding: 6px 12px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
}

.filter-btn:hover {
    box-shadow: var(--shadow);
    transform: translateY(-1px);
}

.filter-btn.active {
    background-color: #333;
    border-color: #555;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
    font-weight: 600;
}

/* NEW badge styling */
.filter-btn .count-badge {
    background: var(--border);
    color: var(--text-secondary);
    border-radius: 999px;
    padding: 0 6px;
    font-size: 11px;
    line-height: 16px;
    min-width: 18px;
    text-align: center;
}

/* Remove generic active override */
/* .filter-btn.active .count-badge { background: var(--primary); color: var(--surface); } */

/* Status-specific badge colours */
.all-filter .count-badge { background: var(--border); color: var(--text); }
.ok-filter .count-badge { background: var(--success); color: #fff; }
.warning-filter .count-badge { background: var(--warning); color: #fff; }
.critical-filter .count-badge { background: var(--critical); color: #fff; }
.unknown-filter .count-badge { background: var(--unknown); color: #fff; }
.pending-filter .count-badge { background: var(--pending); color: #fff; }

/* Status-specific filter buttons */
.ok-filter.active {
    background-color: var(--success-bg);
    border-color: var(--success);
    color: var(--success);
}

.warning-filter.active {
    background-color: var(--warning-bg);
    border-color: var(--warning);
    color: var(--warning);
}

.critical-filter.active {
    background-color: var(--critical-bg);
    border-color: var(--critical);
    color: var(--critical);
}

.unknown-filter.active {
    background-color: var(--unknown-bg);
    border-color: var(--unknown);
    color: var(--unknown);
}

.pending-filter.active {
    background-color: var(--pending-bg);
    border-color: var(--pending);
    color: var(--pending);
}

/* Responsive styles for filters */
@media (max-width: 768px) {
    .filter-container {
        margin-top: 10px;
        gap: 8px;
    }
    
    .filter-label {
        font-size: 13px;
        width: 100%;
        margin-bottom: 4px;
    }
    
    .filter-buttons {
        width: 100%;
        overflow-x: auto;
        padding-bottom: 6px;
        flex-wrap: nowrap;
    }
    
    .filter-buttons::-webkit-scrollbar {
        height: 4px;
    }
    
    .filter-buttons::-webkit-scrollbar-track {
        background: transparent;
    }
    
    .filter-buttons::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
    }
    
    .filter-btn {
        padding: 4px 10px;
        font-size: 12px;
        white-space: nowrap;
    }
}

.selection-mode-button {
    background-color: var(--surface);
    border: 1px solid var(--border);
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--text);
    transition: all 0.2s ease;
    flex-shrink: 0;
    margin-left: 8px;
}

.selection-mode-button:hover {
    background-color: #333;
    transform: scale(1.05);
}

.selection-mode-button i {
    font-size: 16px;
}

.selection-mode .service-card {
    cursor: pointer;
    position: relative;
}

.selection-mode .service-card::before {
    content: '';
    position: absolute;
    top: 10px;
    right: 10px;
    width: 20px;
    height: 20px;
    border: 2px solid var(--border);
    border-radius: 4px;
    background-color: var(--surface);
    z-index: 5;
}

.selection-mode .service-card.selected::before {
    content: '✓';
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--primary);
    color: white;
    border-color: var(--primary);
}

.multi-delete-button {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: var(--critical);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 30px;
    cursor: pointer;
    font-weight: bold;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    z-index: 100;
    display: none;
    align-items: center;
    justify-content: center;
}

.multi-delete-button:hover {
    background-color: #d32f2f;
}

.modal-refresh {
    cursor: pointer;
    color: var(--text-secondary);
    font-size: 14px;
    padding: 3px;
    transition: all 0.2s ease;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 50%;
    width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 12px;
    right: 106px;
}

.modal-refresh:hover {
    color: var(--text);
    background: rgba(0, 0, 0, 0.1);
    transform: scale(1.05) rotate(90deg);
}

/* Rename Service Modal Styles */
.rename-form {
    padding: 0;
}

.rename-form .modal-row {
    margin-bottom: 18px;
    padding-bottom: 18px;
    border-bottom: 1px solid var(--border);
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.rename-form .modal-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.rename-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--text);
    font-size: 14px;
}

.rename-form input[type="text"] {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border);
    border-radius: 8px;
    font-size: 14px;
    background-color: var(--surface);
    color: var(--text);
    box-sizing: border-box;
    transition: border-color 0.2s ease;
}

.rename-form input[type="text"]:focus {
    outline: none;
    border-color: var(--success);
}

.rename-form input[readonly] {
    background-color: var(--background);
    color: var(--text-secondary);
    cursor: not-allowed;
}

.rename-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 0;
}

.rename-btn {
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.2s ease;
    border: none;
}

.rename-btn-primary {
    background-color: #333;
    color: var(--text);
    border: 1px solid #555;
}

.rename-btn-primary:hover {
    box-shadow: var(--shadow);
    transform: translateY(-1px);
}

.rename-btn-secondary {
    background-color: var(--surface);
    color: var(--text);
    border: 1px solid var(--border);
}

.rename-btn-secondary:hover {
    background-color: var(--background);
}

.availability-container {
    background: transparent; /* Remove background since it's now in a container */
    border: none; /* Remove border since it's in a container */
    border-radius: 0; /* Remove border radius since it's in a container */
    padding: 0; /* Remove padding since it's in a container */
    margin-bottom: 0; /* Remove margin since it's in a container */
    overflow: hidden;
    flex: 1; /* Take up remaining space */
    min-width: 0; /* Allow shrinking */
}

.availability-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
}

.availability-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    align-items: center;
}

.availability-controls input[type="datetime-local"] {
    flex: 1 1 140px;
    min-width: 0;
    background: var(--surface);
    color: var(--text);
    border: 1px solid var(--border);
    border-radius: 4px;
    padding: 4px 6px;
    font-size: 12px;
    accent-color: var(--primary);
}

.availability-controls input[type="datetime-local"]::-webkit-calendar-picker-indicator {
    opacity: 0.8;
    cursor: pointer;
    filter: invert(1) brightness(1.2);
}

/* Browser Scrollbar Styles */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--surface);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--border);
    border-radius: 4px;
    border: 1px solid var(--surface);
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

::-webkit-scrollbar-corner {
    background: var(--surface);
}

/* Firefox scrollbar styles */
* {
    scrollbar-width: thin;
    scrollbar-color: var(--border) var(--surface);
}

.availability-refresh {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: 4px;
    padding: 4px 8px;
    cursor: pointer;
}

.availability-refresh:hover {
    background: var(--hover);
}

.availability-refresh i {
    color: var(--text);
    pointer-events: none;
}

.availability-refresh:hover i {
    color: var(--text);
}

/* calendar icon tweak for firefox */
.availability-controls input[type="datetime-local"]::-moz-calendar-picker-indicator {
    filter: invert(1) brightness(1.8);
}
.availability-controls input[type="datetime-local"]::-webkit-calendar-picker-indicator {
    filter: invert(1) brightness(1.8);
}

/* PDF export button styling */
.availability-export {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: 4px;
    padding: 4px 8px;
    cursor: pointer;
    color: var(--text);
    transition: all 0.2s ease;
}

.availability-export:hover {
    background: #333;
    border-color: #555;
}

.availability-export:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.availability-export i {
    color: var(--text);
    pointer-events: none;
}

/* PDF Export Modal Styles */
.pdf-export-form {
    padding: 20px;
}

.pdf-export-form .modal-row {
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.pdf-export-form label {
    font-weight: 600;
    color: var(--text);
    font-size: 14px;
}

.pdf-export-form input[type="datetime-local"] {
    width: 100%;
    min-width: 200px;
    max-width: 300px;
    background: var(--surface);
    color: var(--text);
    border: 1px solid var(--border);
    border-radius: 4px;
    padding: 10px 12px;
    font-size: 14px;
    accent-color: var(--primary);
    height: 40px;
}

.pdf-export-form input[type="datetime-local"]:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.2);
}

.pdf-export-form input[type="datetime-local"]::-webkit-calendar-picker-indicator {
    opacity: 0.8;
    cursor: pointer;
    filter: invert(1) brightness(1.2);
}

.pdf-export-form input[type="datetime-local"]::-moz-calendar-picker-indicator {
    filter: invert(1) brightness(1.8);
}

.availability-chart {
    width: 100%;
    height: 230px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.availability-chart svg {
    width: 100%;
    height: 100%;
}

.availability-flex {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 24px;
}

.availability-legend {
    display: flex;
    flex-direction: column;
    gap: 4px;
    font-size: 13px;
    color: var(--text);
}

.availability-legend-item {
    display: flex;
    align-items: center;
}

.availability-legend-box {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    margin-right: 6px;
}

.availability-donut {
    width: 160px;
    height: 160px;
    flex: 0 0 auto;
}

.availability-donut svg {
    width: 100% !important;
    height: 100% !important;
}

/* Availability tab styles - separate from main modal tabs */
.availability-tabs {
    margin-bottom: 8px;
    display: flex;
    gap: 4px;
}

.availability-tab {
    padding: 10px 16px;
    background: transparent;
    border: none;
    border-bottom: 2px solid transparent;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 15px;
    font-weight: 500;
    transition: all 0.2s ease;
    flex-shrink: 0;
    position: relative;
    margin-right: 8px;
}

.availability-tab:hover {
    color: var(--text);
}

.availability-tab.active {
    color: var(--text);
    border-bottom: 2px solid var(--success);
    font-weight: 600;
}

@media (max-width: 600px) {
    .availability-flex {
        flex-direction: column;
    }
    .availability-donut {
        width: 120px;
        height: 120px;
    }
    .availability-legend {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
    }
    .availability-controls {
        flex-direction: column;
        align-items: stretch;
    }
    .availability-controls input[type="datetime-local"] {
        flex: 1 1 auto;
        width: 100%;
    }
}

/* SPM Marketing Container (next to host card) */
.spm-marketing-container {
    flex: 1;
    min-width: 300px;
    padding: 20px;
    background: var(--surface);
    border-radius: var(--radius);
    border: none;
    /* Prevent layout shifts by maintaining consistent height */
    min-height: 100px;
    transition: opacity 0.3s ease, transform 0.3s ease;
    margin: 0;
    box-shadow: none;
}

/* SPM Styles (back to original position) */
.spm-container {
    margin-top: 20px;
    padding: 20px;
    background: var(--surface);
    border-radius: var(--radius);
    border: 1px solid var(--border);
    /* Prevent layout shifts by maintaining consistent height */
    min-height: 100px;
    transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Loading state styling */
.spm-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
    color: var(--text-secondary);
}

.spm-loading i {
    font-size: 24px;
    margin-bottom: 10px;
    color: var(--primary);
}

.spm-loading p {
    margin: 0;
    font-size: 14px;
}

.spm-header {
    margin-bottom: 20px;
    text-align: center;
}

.spm-header h2 {
    margin: 0 0 8px 0;
    color: var(--text);
    font-size: 18px;
}

.spm-header p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 14px;
}

.spm-section {
    margin-bottom: 30px;
}

.spm-section h3 {
    margin: 0 0 15px 0;
    color: var(--text);
    font-size: 16px;
    padding-bottom: 8px;
}

.spm-table-container {
    overflow-x: auto;
    border-radius: var(--radius);
    border: 1px solid var(--border);
}

.spm-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 13px;
}

.spm-table th {
    background: var(--background);
    color: var(--text);
    padding: 10px 8px;
    text-align: left;
    font-weight: 600;
    border-bottom: 2px solid var(--border);
    white-space: nowrap; /* Never wrap headers */
    word-break: normal;
    overflow-wrap: normal;
}

.spm-table td {
    padding: 8px;
    border-bottom: 1px solid var(--border);
    color: var(--text);
    white-space: normal;
    word-break: break-word;
    overflow-wrap: anywhere;
}

/* Force wrapping for very long lists (e.g., VLAN Membership) to avoid overflow */
/* Parent connections table (9 columns): VLAN Membership is the 6th column */
.spm-table td:nth-child(6) {
    white-space: normal;
    overflow-wrap: anywhere;
    line-break: anywhere;
    line-height: 1.3;
    max-height: 6.5em;
    overflow-y: auto;
    vertical-align: middle;
    width: 330px;            /* keep device IP column tight */
    max-width: 330px;
}

/* Parent connections table (9 columns): Device IP is the 7th column */
.spm-table td:nth-child(7):nth-last-child(3) {
    white-space: normal;     /* allow wrapping between IP items */
    word-break: keep-all;    /* but do not break inside an IP */
    overflow-wrap: normal;
    line-height: 1.3;
    max-height: 6.5em;       /* cap height to avoid very tall cells */
    overflow-y: auto;        /* scroll if many IPs */
    vertical-align: middle;
    width: 330px;            /* keep device IP column tight */
    max-width: 330px;
}

/* Child connections table (10 columns): VLAN Membership is the 8th column */
.spm-table td:nth-child(8):nth-last-child(3) {
    white-space: normal;
    overflow-wrap: anywhere;
    line-break: anywhere;
    line-height: 1.3;
    max-height: 6.5em;
    overflow-y: auto;
    vertical-align: middle;
    width: 220px;          /* make this column narrower */
    max-width: 220px;
}

/* Child connections table (10 columns): Switch IP is the 2nd column */
.spm-table th:nth-child(2):nth-last-child(9),
.spm-table td:nth-child(2):nth-last-child(9) {
    min-width: 160px;      /* a bit more room before breaking */
    white-space: nowrap;   /* do not break IPs */
}

/* Keep Type column unbroken on a single line */
/* Parent connections (9 columns): Type is 8th */
.spm-table th:nth-child(8),
.spm-table td:nth-child(8) {
    white-space: nowrap;
    word-break: normal;
    overflow-wrap: normal;
}

/* Child connections (10 columns): Type is 9th */
.spm-table th:nth-child(9):nth-last-child(2),
.spm-table td:nth-child(9):nth-last-child(2) {
    white-space: nowrap;
    word-break: normal;
    overflow-wrap: normal;
}

.spm-table tbody tr:hover {
    background: rgba(255, 255, 255, 0.05);
}

.spm-type-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.spm-type-uplink {
    background: var(--primary);
    color: #fff;
}

.spm-type-device {
    background: var(--success);
    color: #fff;
}

.spm-type-unknown {
    background: var(--unknown);
    color: #fff;
}

.spm-type-n\/a {
    background: var(--unknown);
    color: #fff;
}

.spm-status-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    font-size: 12px;
}

.spm-status-up {
    background: #4CAF50;
    color: #fff;
}

.spm-status-up i {
    color: #fff;
}

.spm-status-down {
    background: #f44336;
    color: #fff;
}

.spm-status-down i {
    color: #fff;
}

.spm-empty, .spm-loading, .spm-error {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-secondary);
}

.spm-empty i, .spm-loading i, .spm-error i {
    font-size: 24px;
    margin-bottom: 10px;
    display: block;
}

.spm-loading i {
    color: var(--primary);
}

.spm-error i {
    color: var(--critical);
}

.spm-empty p, .spm-loading p, .spm-error p {
    margin: 0;
    font-size: 14px;
}

@media (max-width: 768px) {
    .spm-table {
        font-size: 12px;
    }
    
    .spm-table th, .spm-table td {
        padding: 6px 4px;
    }
    
    .spm-type-badge {
        font-size: 10px;
        padding: 1px 6px;
    }
    
    .spm-status-badge {
        width: 20px;
        height: 20px;
        font-size: 10px;
    }
}

/* SPM Marketing Styles */
.spm-marketing {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    text-align: center;
}

.spm-marketing i {
    font-size: 24px;
    color: var(--text-secondary);
    margin-bottom: 15px;
    display: block;
}

.spm-marketing p {
    color: var(--text-secondary);
    font-size: 14px;
    line-height: 1.5;
    margin: 0 0 10px 0;
}

.spm-marketing p:last-child {
    margin-bottom: 0;
}

.spm-marketing strong {
    color: var(--text);
    font-weight: 600;
}

/* Protocol Availability Modal Styles */
.protocol-check-container {
    padding: 20px;
}

.protocol-options {
    margin-bottom: 30px;
}

.protocol-options h3 {
    margin-bottom: 20px;
    color: var(--text);
    font-size: 18px;
    font-weight: 600;
}

.protocol-option {
    display: flex;
    align-items: center;
    padding: 20px;
    margin-bottom: 15px;
    background: var(--surface);
    border: 2px solid var(--border);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.protocol-option:hover {
    border-color: var(--primary);
    background: var(--surface-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.protocol-icon {
    flex: 0 0 60px;
    height: 60px;
    background: var(--primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
}

.protocol-icon i {
    font-size: 24px;
    color: white;
}

.protocol-info {
    flex: 1;
}

.protocol-info h4 {
    margin: 0 0 8px 0;
    color: var(--text);
    font-size: 16px;
    font-weight: 600;
}

.protocol-info p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 14px;
    line-height: 1.4;
}

.protocol-results {
    margin-top: 20px;
}

.protocol-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
}

.protocol-loading i {
    font-size: 32px;
    color: var(--primary);
    margin-bottom: 15px;
}

.protocol-loading p {
    color: var(--text-secondary);
    font-size: 16px;
    margin: 0;
}

.protocol-content {
    background: var(--surface);
    border-radius: 8px;
    padding: 20px;
    border: 1px solid var(--border);
}

.protocol-content .section {
    margin-bottom: 20px;
}

.protocol-content .section:last-child {
    margin-bottom: 0;
}

.protocol-content h3 {
    margin: 0 0 15px 0;
    color: var(--text);
    font-size: 16px;
    font-weight: 600;
    border-bottom: 2px solid var(--border);
    padding-bottom: 8px;
}

@media (max-width: 768px) {
    .protocol-option {
        flex-direction: column;
        text-align: center;
        padding: 15px;
    }
    
    .protocol-icon {
        margin-right: 0;
        margin-bottom: 15px;
    }
    
    .protocol-info h4 {
        font-size: 14px;
    }
    
    .protocol-info p {
        font-size: 13px;
    }
}