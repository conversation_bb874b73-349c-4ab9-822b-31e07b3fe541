<?php
include "loadenv.php";
// Database configuration
define('DB_HOST', $_ENV["DB_SERVER"]);
define('DB_USER', $_ENV["DB_USER"]);
define('DB_PASS', $_ENV["DB_PASSWORD"]);
define('DB_NAME', 'bubblemaps');
define('NAGIOS_USER', $_ENV['NAGIOS_USER']);
define('NAGIOS_PASS', $_ENV['NAGIOS_PASS']);

// Disable SSL verification
$context = stream_context_create([
    'ssl' => [
        'verify_peer' => false,
        'verify_peer_name' => false,
        'allow_self_signed' => true
    ]
]);

function getSelfIp() {
    $ip = shell_exec("cat /etc/sysconfig/ipaddr 2>&1");
    if ($ip === null) {
        die("Error: Unable to retrieve IP address.");
    }
    return trim($ip);
}

function fetchHostGroupList($host) {
    try {
        // Get credentials from the database
        $credentials = getUserCredentials();
        $hostname = getSelfIp();
        
        if ($credentials === null) {
            throw new Exception("Failed to retrieve user credentials");
        }

        $hostname = getSelfIp();
        $url = "https://{$hostname}/nagios/cgi-bin/objectjson.cgi?query=hostgrouplist&hostgroupmember={$host}";
        

        // Initialize cURL session
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_USERPWD, $credentials['tfUsername'] . ":" . $credentials['tfPassword']);
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        
        // Execute request
        $response = curl_exec($ch);
        
        // Handle cURL errors
        if ($response === false) {
            $errorMsg = curl_error($ch);
            curl_close($ch);
            throw new Exception("cURL error: " . $errorMsg);
        }
        
        // Check HTTP status code
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode != 200) {
            throw new Exception("HTTP error! Status: {$httpCode}");
        }

        // Parse JSON response
        $data = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("JSON parse error: " . json_last_error_msg());
        }
        

        // Return hostgrouplist or empty array if not found
        $hostGroupList = $data['data']['hostgrouplist'] ?? [];
        if (empty($hostGroupList)) {
            error_log("Host group list is empty for host: $host");
        }
        return $hostGroupList;
        
    } catch (Exception $e) {
        error_log('Error fetching host group list: ' . $e->getMessage());
        return [];
    }
}

function getHostnameByIP($ip) {
    try {
        // Get credentials from the database
        $credentials = getUserCredentials();
        $hostname = getSelfIp();
        
        if ($credentials === null) {
            throw new Exception("Failed to retrieve user credentials");
        }

        $nagiosUrl = "https://{$hostname}/nagios/cgi-bin/objectjson.cgi?query=hostlist&details=true";
        
        // Initialize cURL session
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $nagiosUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        // Disable SSL verification (as per your previous preference)
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        // Set HTTP Basic Authentication credentials
        curl_setopt($ch, CURLOPT_USERPWD, $credentials['tfUsername'] . ":" . $credentials['tfPassword']);
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        
        // Execute request
        $response = curl_exec($ch);
        
        // Handle cURL errors
        if ($response === false) {
            $errorMsg = curl_error($ch);
            curl_close($ch);
            throw new Exception("cURL error: " . $errorMsg);
        }
        
        // Check HTTP status code
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode != 200) {
            throw new Exception("HTTP error! Status: {$httpCode}");
        }
        
        // Parse JSON response
        $data = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("JSON parse error: " . json_last_error_msg());
        }
        
        // Search for matching IP in hostlist
        if (isset($data['data']['hostlist'])) {
            foreach ($data['data']['hostlist'] as $host) {
                if (isset($host['address']) && $host['address'] === $ip) {
                    return $host['name'];
                }
            }
        }
        
        return null;
        
    } catch (Exception $e) {
        error_log('Error fetching host data: ' . $e->getMessage());
        throw $e;
    }
}

function getDatabaseConnectionAdminUser() {
    $servername = $_ENV["DB_SERVER"];
    $username = $_ENV["DB_USER"];
    $password = $_ENV["DB_PASSWORD"]; 
    $dbname = "blesk";

    $conn = new mysqli($servername, $username, $password, $dbname);

    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    return $conn;
}

function getUserCredentials() {
    $conn = getDatabaseConnectionAdminUser();

    $sql = "SELECT username, password FROM users WHERE user_id = 1";
    $result = $conn->query($sql);

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $userCredentials = [
            "tfUsername" => $row["username"],
            "tfPassword" => $row["password"]
        ];
    } else {
        $userCredentials = null;
    }

    $conn->close();

    return $userCredentials;
}

function simulateVerifyActions($hostname) {
    $actions = ['butValue1', 'butValue2', 'butValue3', 'butValue4'];
    
    try {
        // Initialize cURL session with cookies
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_COOKIEJAR => 'cookies.txt',
            CURLOPT_COOKIEFILE => 'cookies.txt',
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);

        // Login to Nagios
        curl_setopt($ch, CURLOPT_URL, "https://$hostname/nagiosql/index.php");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
            'tfUsername' => NAGIOS_USER,
            'tfPassword' => NAGIOS_PASS,
            'Submit' => 'Login'
        ]));
        
        $response = curl_exec($ch);
        if(curl_errno($ch)) throw new Exception('Login failed: ' . curl_error($ch));

        // Handle redirect
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        if ($status >= 300 && $status < 400) {
            $redirect = curl_getinfo($ch, CURLINFO_REDIRECT_URL);
            curl_setopt($ch, CURLOPT_URL, $redirect);
            curl_exec($ch);
        }

        // Perform verification actions
        curl_setopt($ch, CURLOPT_URL, "https://$hostname/nagiosql/admin/verify.php");
        foreach ($actions as $action) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, "$action=Do+it");
            $response = curl_exec($ch);
            if(curl_errno($ch)) throw new Exception("Action $action failed");
        }
        
        curl_close($ch);
    } catch (Exception $e) {
        error_log($e->getMessage());
    }
}

function importHostToAPM($hostname, $ip, $scanfile) {
    try {
        echo "Importing host to APM...\n";
        $ch = curl_init("https://$hostname/ndd/import-proc.php");
        curl_setopt_array($ch, [
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => [
                'caller' => 'host-ip',
                'subnet' => '',
                'url_ip' => $ip,
                'scanfile' => $scanfile
            ],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);
        
        $response = curl_exec($ch);
        if(curl_errno($ch)) throw new Exception('Import failed: ' . curl_error($ch));
        
        $effectiveUrl = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
        parse_str(parse_url($effectiveUrl, PHP_URL_QUERY), $params);
        return $params['status'] ?? 'Import status unknown';
    } catch (Exception $e) {
        error_log($e->getMessage());
        return 'Error occurred';
    }
}

function scanHostForAPM($hostname, $ip) {
    try {
        echo "Scanning host for APM...\n";
        $ch = curl_init("https://$hostname/ndd/host-ip-scan-proc.php");
        curl_setopt_array($ch, [
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => [
                'iprange' => $ip,
                'caller' => 'host-ip',
                'subnet' => '',
                'url_ip' => $ip
            ],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);
        
        $response = curl_exec($ch);
        if(curl_errno($ch)) throw new Exception('Scan failed: ' . curl_error($ch));
        
        $effectiveUrl = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
        parse_str(parse_url($effectiveUrl, PHP_URL_QUERY), $params);
        return $params['scanfile'] ?? null;
    } catch (Exception $e) {
        error_log($e->getMessage());
        return null;
    }
}

function backgroundScanAPM() {
    $hostname = gethostname();
    
    $db = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    if ($db->connect_error) {
        die("DB connection failed: " . $db->connect_error);
    }

    while (true) {
        // Get hosts with apmStatus = 'not-added'
        $result = $db->query("SELECT ip FROM hosts WHERE apmStatus = 'not-added'");
        
        if ($result === false) {
            error_log("SELECT failed: " . $db->error);
            continue; // Skip to next loop iteration if SELECT fails
        }

        if ($result->num_rows === 0) {
            // No rows to process, exit the loop or add a break condition
            break;  // Exit the loop if no rows are found
        }

        while ($row = $result->fetch_assoc()) {
            $ip = $db->real_escape_string($row['ip']);

            try {   
                if (!checkHostIsUp($ip)) {
                    echo "Host $ip is down. Removing from database...\n";
                    $deleteQuery = "DELETE FROM hosts WHERE ip = '$ip'";
                    if (!$db->query($deleteQuery)) {
                        error_log("Failed to delete unreachable host $ip: " . $db->error);
                    } else {
                        error_log("Deleted unreachable host $ip from database");
                    }
                    continue;
                }

                $scanfile = scanHostForAPM($hostname, $ip);
                if (!$scanfile) {
                    throw new Exception("Scan failed for $ip");
                }
                echo "Scan complete...\n";

                $status = importHostToAPM($hostname, $ip, $scanfile);
                echo "Import complete...\n";
                sleep(1);
                $newStatus = stripos($status, 'success') !== false ? 'pending' : 'error';
                echo "Update: $ip -> $newStatus\n";

                $updateQuery = "UPDATE hosts SET apmStatus = '$newStatus' WHERE ip = '$ip'";
                if (!$db->query($updateQuery)) {
                    error_log("Failed to update host for $ip: " . $db->error);
                } else {
                    error_log("Successfully updated $ip");
                }

            } catch (Exception $e) {
                error_log($e->getMessage());
            }
        }
    }
    echo "No more rows to process. Exiting...\n";
}

// Update hostgroups

function updateHostGroups() {
    // Establish database connection
    $db = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    if ($db->connect_error) {
        die("DB connection failed: " . $db->connect_error);
    }

    // Query for IPs where hostgroup is NULL
    $result = $db->query("SELECT ip FROM hosts WHERE hostgroup IS NULL AND apmStatus != 'ask'");
    
    if ($result === false) {
        error_log("SELECT failed: " . $db->error);
        $db->close();
        return;
    }

    if ($result->num_rows === 0) {
        echo "No hosts with NULL hostgroup found.\n";
        $db->close();
        return;
    }

    while ($row = $result->fetch_assoc()) {
        $ip = $db->real_escape_string($row['ip']);

        try {
            // Get the hostname by IP
            $realHostname = getHostnameByIP($ip);
            if ($realHostname === null) {
                throw new Exception("Could not retrieve hostname for IP: $ip");
            }

            // Fetch the host group list using the retrieved hostname
            $hostGroups = fetchHostGroupList($realHostname);
            if (empty($hostGroups)) {
                error_log("No host groups found for $realHostname (IP: $ip)");
                continue; // Skip to next IP if no host groups are found
            }

            // Encode the entire host groups array as JSON
            $hostGroupJson = json_encode($hostGroups);
            if ($hostGroupJson === false) {
                throw new Exception("Failed to encode host groups to JSON for IP: $ip");
            }

            // Escape the JSON string for safe SQL insertion
            $hostGroupJsonSafe = $db->real_escape_string($hostGroupJson);

            // Update the hosts table with the JSON-encoded host groups
            $updateQuery = "UPDATE hosts SET hostgroup = '$hostGroupJsonSafe' WHERE ip = '$ip'";
            if (!$db->query($updateQuery)) {
                error_log("Failed to update hostgroup for $ip: " . $db->error);
            } else {
                error_log("Successfully updated hostgroup '$hostGroupJson' for $ip");
            }

        } catch (Exception $e) {
            error_log("Error processing $ip: " . $e->getMessage());
        }
    }

    echo "Host group update process completed.\n";
    $db->close();
}


// Function to read the lock status
function isLocked($lockFile) {
    if (!file_exists($lockFile)) {
        return false;
    }
    
    $fp = @fopen($lockFile, 'r');
    if (!$fp) {
        return false;
    }
    
    // Try to get a shared lock (non-blocking)
    $locked = !flock($fp, LOCK_SH | LOCK_NB);
    fclose($fp);
    
    return $locked;
}

// Function to set the lock status
function setLock($lockFile, $status) {
    global $lockHandle;
    
    if ($status) {
        // Create or open the lock file
        $lockHandle = fopen($lockFile, 'w');
        if (!$lockHandle) {
            throw new Exception("Unable to create lock file: $lockFile");
        }
        
        // Try to acquire an exclusive lock (non-blocking)
        if (!flock($lockHandle, LOCK_EX | LOCK_NB)) {
            fclose($lockHandle);
            throw new Exception("Unable to acquire lock: $lockFile");
        }
        
        // Write PID to the lock file
        fwrite($lockHandle, getmypid());
        fflush($lockHandle);
    } else if (isset($lockHandle) && is_resource($lockHandle)) {
        // Release the lock
        flock($lockHandle, LOCK_UN);
        fclose($lockHandle);
        
        // Remove the lock file
        if (file_exists($lockFile)) {
            @unlink($lockFile);
        }
    }
}

function checkHostIsUp($ip) {
    $command = "sudo nmap -sn " . escapeshellarg($ip) . " 2>&1";
    $output = shell_exec($command);
    // The output of nmap will include "Host is up" when the host responds to ping/arp
    return (strpos($output, "Host is up") !== false);
}

// Script start

// Disallow the script to run multiple instances if already running
$lockFile = dirname(__FILE__) . '/locks/background_scan.lock';
$lockHandle = null;

// Check if the script is already running
if (isLocked($lockFile)) {
    echo "Script is already running. Exiting.\n";
    exit;
}

// Set the lock
try {
    setLock($lockFile, true);
    
    // Start the scanning and importing process
    backgroundScanAPM();

    // In case we missed any hosts, recheck if all host groups are added to the database
    updateHostGroups();

    echo "Script completed successfully.\n";
} catch (Exception $e) {
    // Handle any errors that occur during execution
    echo "Error: " . $e->getMessage() . "\n";
} finally {
    // Always release the lock when the script finishes
    setLock($lockFile, false);
}