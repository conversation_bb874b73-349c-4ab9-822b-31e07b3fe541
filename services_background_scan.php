<?php
include "loadenv.php";
// Database configuration
define('DB_HOST', $_ENV["DB_SERVER"]);
define('DB_USER', $_ENV["DB_USER"]);
define('DB_PASS', $_ENV["DB_PASSWORD"]);
define('DB_NAME', 'bubblemaps');

// Function to read bubblemap configs
function readBubblemapConfig($key) {
    $configFile = dirname(__FILE__) . '/conf/bubblemap_configs';
    if (!file_exists($configFile)) {
        return null;
    }
    
    $lines = file($configFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, $key . ':') === 0) {
            return trim(substr($line, strlen($key) + 1));
        }
    }
    return null;
}

// Function to check if service discovery is enabled
function isServiceDiscoveryEnabled() {
    $setting = readBubblemapConfig('service discovery');
    return $setting === 'on';
}

// Disable SSL verification
$context = stream_context_create([
    'ssl' => [
        'verify_peer' => false,
        'verify_peer_name' => false,
        'allow_self_signed' => true
    ]
]);

function getSelfIp() {
    $ip = shell_exec("cat /etc/sysconfig/ipaddr 2>&1");
    if ($ip === null) {
        die("Error: Unable to retrieve IP address.");
    }
    return trim($ip);
}

function scanHostForAPM($hostname, $ip) {
    try {
        $ch = curl_init("https://$hostname/ndd/host-ip-scan-proc.php");
        curl_setopt_array($ch, [
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => [
                'iprange' => $ip,
                'caller' => 'host-ip',
                'subnet' => '',
                'url_ip' => $ip
            ],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);
        
        $response = curl_exec($ch);
        if(curl_errno($ch)) throw new Exception('Scan failed: ' . curl_error($ch));
        
        $effectiveUrl = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
        parse_str(parse_url($effectiveUrl, PHP_URL_QUERY), $params);
        return $params['scanfile'] ?? null;
    } catch (Exception $e) {
        error_log($e->getMessage());
        return null;
    }
}

// Function to read the lock status
function isLocked($lockFile) {
    if (!file_exists($lockFile)) {
        return false;
    }
    
    $fp = @fopen($lockFile, 'r');
    if (!$fp) {
        return false;
    }
    
    // Try to get a shared lock (non-blocking)
    $locked = !flock($fp, LOCK_SH | LOCK_NB);
    fclose($fp);
    
    return $locked;
}

// Function to set the lock status
function setLock($lockFile, $status) {
    global $lockHandle;
    
    if ($status) {
        // Create or open the lock file
        $lockHandle = fopen($lockFile, 'w');
        if (!$lockHandle) {
            throw new Exception("Unable to create lock file: $lockFile");
        }
        
        // Try to acquire an exclusive lock (non-blocking)
        if (!flock($lockHandle, LOCK_EX | LOCK_NB)) {
            fclose($lockHandle);
            throw new Exception("Unable to acquire lock: $lockFile");
        }
        
        // Write PID to the lock file
        fwrite($lockHandle, getmypid());
        fflush($lockHandle);
    } else if (isset($lockHandle) && is_resource($lockHandle)) {
        // Release the lock
        flock($lockHandle, LOCK_UN);
        fclose($lockHandle);
        
        // Remove the lock file
        if (file_exists($lockFile)) {
            @unlink($lockFile);
        }
    }
}

/**
 * Function to scan hosts with statuses other than ask, not-added, added, and pending
 * This function only scans the hosts but doesn't import them to APM
 */
function backgroundScanAPMExclude() {
    $hostname = gethostname();
    echo "Starting background scan for hosts with special statuses...\n";
    
    $db = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    if ($db->connect_error) {
        die("DB connection failed: " . $db->connect_error);
    }

    // Get hosts with apmStatus not in the excluded list and services not set to "DO-NOT-SCAN"
    $result = $db->query("SELECT h.ip, h.hostname, h.infra, h.muted FROM hosts h WHERE h.apmStatus != 'ask' AND h.apmStatus != 'not-added' AND h.apmStatus != 'added' AND h.apmStatus != 'pending' AND h.blacklist != 1 AND NOT EXISTS (SELECT 1 FROM servicesPending sp WHERE sp.host_ip = h.ip AND sp.services = '[\"DO-NOT-SCAN\"]')");
    
    if ($result === false) {
        error_log("SELECT failed: " . $db->error);
        echo "Failed to query hosts with special statuses.\n";
        $db->close();
        return;
    }

    if ($result->num_rows === 0) {
        echo "No hosts with special statuses found.\n";
        $db->close();
        return;
    }
    
    echo "Found " . $result->num_rows . " hosts with special statuses to scan.\n";

    while ($row = $result->fetch_assoc()) {
        $ip = $db->real_escape_string($row['ip']);
        $hostName = $db->real_escape_string($row['hostname'] ?? '');
        $infra = $db->real_escape_string($row['infra'] ?? '');
        $isMuted = (bool)($row['muted'] ?? 0);

        try {
            echo "Scanning IP: $ip ...\n";
            $scanfile = scanHostForAPM($hostname, $ip);
            
            if (!$scanfile) {
                echo "Scan failed for $ip\n";
                error_log("Scan failed for $ip");
                continue;
            }
            
            echo "Scan completed successfully for $ip (scan file: $scanfile)\n";
            error_log("Scan completed successfully for $ip (scan file: $scanfile)");
            
            // Fetch the host-ip.php page to check for available services
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => "https://$hostname/ndd/host-ip.php?ip=$ip&scanfile=$scanfile",
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false
            ]);
            
            $response = curl_exec($ch);
            
            if ($response === false) {
                echo "Failed to fetch service information for $ip: " . curl_error($ch) . "\n";
                error_log("Failed to fetch service information for $ip: " . curl_error($ch));
            } else {
                $validItems = [];

                // Extract services that can be added
                if (preg_match('/<h3>Services that can be added<\/h3>(.*?)(?:<h3>|$)/s', $response, $matches)) {
                    $servicesHtml = $matches[1];
                    
                    // Extract valid service names with a better regex pattern
                    preg_match_all('/\* ([A-Za-z0-9][A-Za-z0-9_\-\.\s]+(?:\-[A-Za-z0-9\s]+)*)/', $servicesHtml, $serviceMatches);
                    
                    if (!empty($serviceMatches[1])) {
                        foreach ($serviceMatches[1] as $service) {
                            $trimmed = trim($service);
                            // Skip empty services or HTML artifacts
                            if ($trimmed !== '' && $trimmed !== '-->' && strpos($trimmed, '<') === false && strpos($trimmed, '>') === false) {
                                $validItems[] = $trimmed;
                            }
                        }
                    }
                }
                
                // Extract port interfaces that can be added
                if (preg_match('/<h3>Port interfaces that can be added<\/h3>(.*?)(?:<h3>|$)/s', $response, $portMatches)) {
                    $portsHtml = $portMatches[1];
                    
                    // Extract valid port interface names with the same pattern as services
                    preg_match_all('/\* ([A-Za-z0-9][A-Za-z0-9_\-\.\s]+(?:\-[A-Za-z0-9\s]+)*)/', $portsHtml, $portServiceMatches);
                    
                    if (!empty($portServiceMatches[1])) {
                        foreach ($portServiceMatches[1] as $port) {
                            $trimmed = trim($port);
                            // Skip empty ports or HTML artifacts
                            if ($trimmed !== '' && $trimmed !== '-->' && strpos($trimmed, '<') === false && strpos($trimmed, '>') === false) {
                                $validItems[] = $trimmed;
                            }
                        }
                    }
                }
                
                // Extract managed APs that can be added
                if (preg_match('/<h3>Managed APs that can be added<\/h3>(.*?)(?:<h3>|$)/s', $response, $apMatches)) {
                    $apsHtml = $apMatches[1];
                    
                    // Extract valid AP names with the same pattern as services
                    preg_match_all('/\* ([A-Za-z0-9][A-Za-z0-9_\-\.\s]+(?:\-[A-Za-z0-9\s]+)*)/', $apsHtml, $apServiceMatches);
                    
                    if (!empty($apServiceMatches[1])) {
                        foreach ($apServiceMatches[1] as $ap) {
                            $trimmed = trim($ap);
                            // Skip empty APs or HTML artifacts
                            if ($trimmed !== '' && $trimmed !== '-->' && strpos($trimmed, '<') === false && strpos($trimmed, '>') === false) {
                                $validItems[] = $trimmed;
                            }
                        }
                    }
                }
                
                // Extract FortiGate tunnels that can be added
                if (preg_match('/<h3>Fortigate tunnels that can be added<\/h3>(.*?)(?:<h3>|$)/s', $response, $tunnelMatches)) {
                    $tunnelsHtml = $tunnelMatches[1];
                    
                    // Extract valid tunnel names with the same pattern as services
                    preg_match_all('/\* ([A-Za-z0-9][A-Za-z0-9_\-\.\s]+(?:\-[A-Za-z0-9\s]+)*)/', $tunnelsHtml, $tunnelServiceMatches);
                    
                    if (!empty($tunnelServiceMatches[1])) {
                        foreach ($tunnelServiceMatches[1] as $tunnel) {
                            $trimmed = trim($tunnel);
                            // Skip empty tunnels or HTML artifacts
                            if ($trimmed !== '' && $trimmed !== '-->' && strpos($trimmed, '<') === false && strpos($trimmed, '>') === false) {
                                $validItems[] = $trimmed;
                            }
                        }
                    }
                }
                
                // Extract MSSQL services that can be added
                if (preg_match('/<h3>MSSQL services that can be added<\/h3>(.*?)(?:<h3>|$)/s', $response, $mssqlMatches)) {
                    $mssqlHtml = $mssqlMatches[1];
                    
                    // Extract valid MSSQL service names with the same pattern as services
                    preg_match_all('/\* ([A-Za-z0-9][A-Za-z0-9_\-\.\s]+(?:\-[A-Za-z0-9\s]+)*)/', $mssqlHtml, $mssqlServiceMatches);
                    
                    if (!empty($mssqlServiceMatches[1])) {
                        foreach ($mssqlServiceMatches[1] as $mssql) {
                            $trimmed = trim($mssql);
                            // Skip empty MSSQL services or HTML artifacts
                            if ($trimmed !== '' && $trimmed !== '-->' && strpos($trimmed, '<') === false && strpos($trimmed, '>') === false) {
                                $validItems[] = $trimmed;
                            }
                        }
                    }
                }
                
                // Extract Oracle services that can be added
                if (preg_match('/<h3>Oracle services that can be added<\/h3>(.*?)(?:<h3>|$)/s', $response, $oracleMatches)) {
                    $oracleHtml = $oracleMatches[1];
                    
                    // Extract valid Oracle service names with the same pattern as services
                    preg_match_all('/\* ([A-Za-z0-9][A-Za-z0-9_\-\.\s]+(?:\-[A-Za-z0-9\s]+)*)/', $oracleHtml, $oracleServiceMatches);
                    
                    if (!empty($oracleServiceMatches[1])) {
                        foreach ($oracleServiceMatches[1] as $oracle) {
                            $trimmed = trim($oracle);
                            // Skip empty Oracle services or HTML artifacts
                            if ($trimmed !== '' && $trimmed !== '-->' && strpos($trimmed, '<') === false && strpos($trimmed, '>') === false) {
                                $validItems[] = $trimmed;
                            }
                        }
                    }
                }
                
                // Extract MySQL services that can be added
                if (preg_match('/<h3>MySQL services that can be added<\/h3>(.*?)(?:<h3>|$)/s', $response, $mysqlMatches)) {
                    $mysqlHtml = $mysqlMatches[1];
                    
                    // Extract valid MySQL service names with the same pattern as services
                    preg_match_all('/\* ([A-Za-z0-9][A-Za-z0-9_\-\.\s]+(?:\-[A-Za-z0-9\s]+)*)/', $mysqlHtml, $mysqlServiceMatches);
                    
                    if (!empty($mysqlServiceMatches[1])) {
                        foreach ($mysqlServiceMatches[1] as $mysql) {
                            $trimmed = trim($mysql);
                            // Skip empty MySQL services or HTML artifacts
                            if ($trimmed !== '' && $trimmed !== '-->' && strpos($trimmed, '<') === false && strpos($trimmed, '>') === false) {
                                $validItems[] = $trimmed;
                            }
                        }
                    }
                }
                
                // Convert array to JSON for database storage
                $itemsJson = json_encode($validItems);
                
                if (!empty($validItems)) {
                    // Check if entry already exists for this IP
                    $checkQuery = "SELECT id, services FROM servicesPending WHERE host_ip = '$ip'";
                    $checkResult = $db->query($checkQuery);
                    
                    if ($checkResult && $checkResult->num_rows > 0) {
                        echo "\n==== EXISTING HOST LOGIC FOR $ip ====\n";
                        $previousEntry = $checkResult->fetch_assoc();
                        $previousServices = json_decode($previousEntry['services'], true);
                        if (!is_array($previousServices)) {
                            echo "WARNING: previousServices was not a valid array, setting to empty array\n";
                            $previousServices = [];
                        }

                        // Normalize both arrays: trim and lowercase all entries
                        $normalize = function($arr) {
                            return array_map(function($s) { return strtolower(trim($s)); }, $arr);
                        };
                        $normalizedValidItems = $normalize($validItems);
                        $normalizedPreviousServices = $normalize($previousServices);

                        // Sort both arrays to avoid order-based mismatches
                        sort($normalizedValidItems);
                        sort($normalizedPreviousServices);

                        echo "Current services: " . implode(", ", $validItems) . "\n";
                        echo "Previous services: " . ($previousEntry['services'] ? $previousEntry['services'] : "none") . "\n";
                        
                        // More thorough comparison - only find truly new services
                        $newServices = [];
                        foreach ($normalizedValidItems as $service) {
                            if (!in_array($service, $normalizedPreviousServices)) {
                                $newServices[] = $service;
                                echo "TRULY NEW SERVICE DETECTED: $service\n";
                            }
                        }

                        echo "Normalized current: " . implode(", ", $normalizedValidItems) . "\n";
                        echo "Normalized previous: " . implode(", ", $normalizedPreviousServices) . "\n";
                        echo "New services detected: " . (count($newServices) > 0 ? implode(", ", $newServices) : "NONE") . "\n";
                        
                        // Only unmute if we have TRULY new services and the host is muted
                        if ($isMuted && count($newServices) > 0) {
                            echo "CONFIRMED: Found " . count($newServices) . " truly new services - UNMUTING HOST\n";
                            $unmuteQuery = "UPDATE hosts SET muted = 0 WHERE ip = '$ip'";
                            if ($db->query($unmuteQuery)) {
                                echo "Host $ip UNMUTED due to new services detected\n";
                                error_log("Host $ip unmuted due to new services detected");
                            }
                        } else if ($isMuted) {
                            echo "Host is muted but NO NEW services detected - keeping muted\n";
                        }
                        
                        // Update existing entry
                        $updateQuery = "UPDATE servicesPending SET host_name = '$hostName', infra = '$infra', services = '$itemsJson' WHERE host_ip = '$ip'";
                        
                        if (!$db->query($updateQuery)) {
                            echo "Failed to update service information for $ip: " . $db->error . "\n";
                            error_log("Failed to update service information for $ip: " . $db->error);
                        } else {
                            echo "Successfully updated service information for $ip\n";
                        }
                    } else {
                        echo "\n==== NEW HOST LOGIC FOR $ip ====\n";
                        // If this is a new entry and the host was muted, unmute it
                        if ($isMuted) {
                            echo "CONFIRMED: This is a NEW host entry in servicesPending - UNMUTING HOST\n";
                            $unmuteQuery = "UPDATE hosts SET muted = 0 WHERE ip = '$ip'";
                            if ($db->query($unmuteQuery)) {
                                echo "Host $ip UNMUTED due to new services detected\n";
                                error_log("Host $ip unmuted due to new services detected");
                            }
                        }
                        
                        // Insert new entry
                        $insertQuery = "INSERT INTO servicesPending (host_ip, host_name, infra, services) 
                                       VALUES ('$ip', '$hostName', '$infra', '$itemsJson')";
                        
                        if (!$db->query($insertQuery)) {
                            echo "Failed to save service information for $ip: " . $db->error . "\n";
                            error_log("Failed to save service information for $ip: " . $db->error);
                        } else {
                            echo "Successfully saved service information for $ip\n";
                        }
                    }
                    
                    echo "Services and port interfaces that can be added for $ip:\n";
                    foreach ($validItems as $item) {
                        echo "* " . $item . "\n";
                    }
                } else {
                    echo "No new services or port interfaces detected for $ip\n";
                }
            }
            
            curl_close($ch);
            
            // Note: We're intentionally not importing to APM as requested
            
        } catch (Exception $e) {
            echo "Error while scanning $ip: " . $e->getMessage() . "\n";
            error_log("Error while scanning $ip: " . $e->getMessage());
        }
    }

    echo "Special status hosts scan completed.\n";
    $db->close();
}

// Script start - Check if service discovery is enabled
if (!isServiceDiscoveryEnabled()) {
    echo "Service discovery is disabled. Exiting.\n";
    exit;
}

// Check if already running using locking mechanism
$lockFile = dirname(__FILE__) . '/locks/services_background_scan.lock';
$lockHandle = null;

if (isLocked($lockFile)) {
    echo "Services background scan is already running. Exiting.\n";
    exit;
}

// Set the lock
try {
    setLock($lockFile, true);
    
    // Execute the scan function
    backgroundScanAPMExclude();
    
    echo "Services background scan completed successfully.\n";
} catch (Exception $e) {
    // Handle any errors that occur during execution
    echo "Error: " . $e->getMessage() . "\n";
} finally {
    // Always release the lock when the script finishes
    setLock($lockFile, false);
} 