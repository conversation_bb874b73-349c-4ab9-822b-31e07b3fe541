<?php
/**
 * Azure resource discovery & NagiosQL provisioning endpoint
 *
 * Supported actions (via query string or JSON body):
 *  - list_resources (GET):  ?action=list_resources&rg=<resource-group>
 *  - list_services  (GET):  ?action=list_services&rg=<resource-group>&rn=<resource-name>
 *  - add_monitoring  (POST JSON): {action:'add_monitoring', rg:'...', rn:'...', services:[...], infra:'...'}
 *
 * NOTE: This script is intentionally self-contained and *does not* rely on the legacy classes
 *       under ndd/ — we speak to NagiosQL DB directly using PDO.
 */

header('Content-Type: application/json');
require_once __DIR__ . '/loadenv.php'; // Provides DB credentials

try {
    // Simple input router --------------------------------------------------
    $method = $_SERVER['REQUEST_METHOD'];

    if ($method === 'GET') {
        $action = $_GET['action'] ?? '';
        if ($action === 'list_resources') {
            listResources();
        } elseif ($action === 'list_services') {
            listServices();
        } else {
            throw new Exception('Unsupported GET action');
        }
    } elseif ($method === 'POST') {
        $payload = json_decode(file_get_contents('php://input'), true);
        if (!is_array($payload)) {
            throw new Exception('Invalid JSON payload');
        }
        $action = $payload['action'] ?? '';
        if ($action === 'add_monitoring') {
            addMonitoring($payload);
        } else {
            throw new Exception('Unsupported POST action');
        }
    } else {
        throw new Exception('Unsupported HTTP method');
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

//--------------------------------------------------------------------------
// Database Functions
//--------------------------------------------------------------------------

function getDatabaseConnection() {
    $servername = $_ENV["DB_SERVER"];
    $username = $_ENV["DB_USER"];
    $password = $_ENV["DB_PASSWORD"]; 
    $dbname = "bubblemaps";

    $conn = new mysqli($servername, $username, $password, $dbname);

    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    return $conn;
}

function getInfrastructureName($infraId) {
    $conn = getDatabaseConnection();
    
    $stmt = $conn->prepare("SELECT name FROM infra WHERE id = ?");
    $stmt->bind_param("i", $infraId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $stmt->close();
        $conn->close();
        return $row['name'];
    }
    
    $stmt->close();
    $conn->close();
    return false;
}

function ensureAzureHostgroup() {
    $db = new PDO('mysql:host=' . $_ENV["DB_SERVER"] . ';dbname=db_nagiosql_v3', $_ENV["DB_USER"], $_ENV["DB_PASSWORD"]);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check if Azure hostgroup exists
    $stmt = $db->prepare('SELECT 1 FROM tbl_hostgroup WHERE hostgroup_name = "Azure"');
    $stmt->execute();
    
    if (!$stmt->fetch()) {
        // Create Azure hostgroup if it doesn't exist
        $stmt = $db->prepare('INSERT INTO tbl_hostgroup (hostgroup_name, alias, config_id, notes, notes_url, action_url, last_modified) VALUES ("Azure", "Azure", 1, "", "", "", NOW())');
        $stmt->execute();
    }
}

function addAzureSubnetToDatabase($infra) {
    $conn = getDatabaseConnection();
    
    // Check if the Azure subnet already exists in the current infrastructure
    $checkSql = "SELECT * FROM subnets WHERE subnet = 'Azure' AND infra = '$infra'";
    $result = $conn->query($checkSql);

    if ($result && $result->num_rows > 0) {
        // Subnet exists in this infrastructure, do nothing and return
        $conn->close();
        return;
    } else {
        // Subnet doesn't exist in this infrastructure, proceed to insert
        $insertSql = "INSERT INTO subnets (subnet, subnetNickname, infra) VALUES ('Azure', 'Azure', '$infra')";
        if (!$conn->query($insertSql)) {
            die("Error inserting Azure subnet: " . $conn->error);
        }
    }

    $conn->close();
}

function addAzureHostToDatabase($resourceName, $infra) {
    $conn = getDatabaseConnection();
    
    // First ensure the Azure subnet exists
    addAzureSubnetToDatabase($infra);
    
    // Check if the host already exists
    $checkSql = "SELECT 1 FROM hosts WHERE hostname = '$resourceName' AND infra = '$infra'";
    $result = $conn->query($checkSql);
    
    if ($result->num_rows > 0) {
        // If record exists, skip insertion
        $conn->close();
        return false;
    }

    // Insert new Azure host entry
    $insertSql = "INSERT INTO hosts (ip, subnet, hostname, infra, apmStatus) VALUES ('$resourceName', 'Azure', '$resourceName', '$infra', 'pending')";
    if (!$conn->query($insertSql)) {
        die("Error: " . $insertSql . "<br>" . $conn->error);
    }
    
    $conn->close();
    return true;
}

//--------------------------------------------------------------------------
// Helpers
//--------------------------------------------------------------------------

function listResources(): void
{
    $rg = trim($_GET['rg'] ?? '');
    if ($rg === '') {
        echo json_encode(['success' => false, 'message' => 'Missing resource-group (rg) parameter']);
        return;
    }

    $cmd = sprintf('/lib64/nagios/plugins/azure/venvAzure/bin/python3 /lib64/nagios/plugins/azure/check_azure.py -rg %s -ls', escapeshellarg($rg));
    exec($cmd . ' 2>&1', $output, $code);

    if ($code !== 0) {
        echo json_encode(['success' => false, 'message' => 'Azure CLI error', 'details' => $output]);
        return;
    }

    $resources = [];
    foreach ($output as $line) {
        // Lines look like: "- Microsoft.Storage/storageAccounts/backupstr001"
        if (preg_match('#-\s+[^/]+/.+?/(.+)#', $line, $m)) {
            $resources[] = trim($m[1]);
        }
    }

    echo json_encode(['success' => true, 'resources' => array_values(array_unique($resources))]);
}

function listServices(): void
{
    $rg = trim($_GET['rg'] ?? '');
    $rn = trim($_GET['rn'] ?? '');
    if ($rg === '' || $rn === '') {
        echo json_encode(['success' => false, 'message' => 'Missing rg or rn parameter']);
        return;
    }

    $cmd = sprintf('/lib64/nagios/plugins/azure/venvAzure/bin/python3 /lib64/nagios/plugins/azure/check_azure.py -rg %s -rn %s --list-services', escapeshellarg($rg), escapeshellarg($rn));
    exec($cmd . ' 2>&1', $output, $code);

    if ($code !== 0) {
        echo json_encode(['success' => false, 'message' => 'Azure CLI error', 'details' => $output]);
        return;
    }

    $services = [];
    foreach ($output as $line) {
        // Each line like: "- Microsoft.Storage/storageAccounts-Availability"
        if (preg_match('#-\s+(.+)#', $line, $m)) {
            $svc = trim($m[1]);
            if ($svc !== '') $services[] = $svc;
        }
    }

    echo json_encode(['success' => true, 'services' => $services]);
}

function addMonitoring(array $payload): void
{
    $rg = trim($payload['rg'] ?? '');
    $rn = trim($payload['rn'] ?? '');
    $services = $payload['services'] ?? [];
    $infraId = trim($payload['infra'] ?? '');

    if ($rg === '' || $rn === '' || empty($services) || $infraId === '') {
        echo json_encode(['success' => false, 'message' => 'Missing required parameters (rg, rn, services, infra)']);
        return;
    }

    // Convert infrastructure ID to infrastructure name
    $infra = getInfrastructureName($infraId);
    if (!$infra) {
        echo json_encode(['success' => false, 'message' => 'Invalid infrastructure ID']);
        return;
    }

    try {
        // 0. Ensure Azure hostgroup exists ------------------------------------------------
        ensureAzureHostgroup();
        
        // 1. Create host config file --------------------------------------------------------
        $hostConfigContent = createAzureHostConfig($rn);
        $hostConfigFile = $rn . '_host.cfg';
        $hostConfigPath = '/etc/nagiosql/import/' . $hostConfigFile;
        
        if (!file_put_contents($hostConfigPath, $hostConfigContent)) {
            throw new Exception("Failed to write host config file: $hostConfigPath");
        }

        // 2. Create service config files -----------------------------------------------------
        $serviceConfigFiles = [];
        foreach ($services as $svc) {
            $serviceConfigContent = createAzureServiceConfig($rn, $svc, $rg);
            $serviceConfigFile = $rn . '_' . sanitizeFilename($svc) . '_service.cfg';
            $serviceConfigPath = '/etc/nagiosql/import/' . $serviceConfigFile;
            
            if (!file_put_contents($serviceConfigPath, $serviceConfigContent)) {
                throw new Exception("Failed to write service config file: $serviceConfigPath");
            }
            $serviceConfigFiles[] = $serviceConfigFile;
        }

        // 3. Run the import process ----------------------------------------------------------
        // Execute the apmimport script for host
        $importResult = execApmImport($hostConfigFile);
        if (!$importResult) {
            throw new Exception("Failed to import host configuration");
        }

        // Execute apmimport for each service
        foreach ($serviceConfigFiles as $serviceFile) {
            $importResult = execApmImport($serviceFile);
            if (!$importResult) {
                throw new Exception("Failed to import service configuration: $serviceFile");
            }
        }

        // 4. Finalize the import -------------------------------------------------------------
        $finishResult = execApmFinish();
        if (!$finishResult) {
            throw new Exception("Failed to finalize import process");
        }

        // 5. Add Azure host to bubblemaps database with "pending" status ------------------
        $hostAdded = addAzureHostToDatabase($rn, $infra);
        if (!$hostAdded) {
            echo json_encode(['success' => false, 'message' => 'Host already exists in database']);
            return;
        }

        echo json_encode([
            'success' => true, 
            'message' => 'Azure monitoring successfully added to database',
            'hostname' => $rn,
            'infra' => $infra
        ]);
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * Create Nagios host configuration content for Azure resource
 */
function createAzureHostConfig(string $resourceName): string
{
    return <<<HOSTCFG
define host {
    host_name                   {$resourceName}
    alias                       {$resourceName}
    display_name                {$resourceName}
    address                     {$resourceName}
    hostgroups                  Azure
    check_command               check_dummy!0
    initial_state               o
    max_check_attempts          3
    active_checks_enabled       1
    check_period                24x7
    flap_detection_enabled      1
    flap_detection_options      o,d,u
    process_perf_data           1
    retain_status_information   1
    contact_groups              network-admins
    notification_interval       0
    notification_period         24x7
    notifications_enabled       1

    _TAGS                       azure
    register                    1
}
HOSTCFG;
}

/**
 * Create Nagios service configuration content for Azure metric
 */
function createAzureServiceConfig(string $hostName, string $serviceName, string $resourceGroup): string
{
    // Extract the service description based on the pattern
    // For Microsoft.Storage/storageAccounts/fileServices-Availability -> fileServices-Availability
    // For Microsoft.Storage/storageAccounts/fileServices-Disk Write/sec -> fileServices-Disk Write/sec
    
    // Find the hyphen in the service name
    $hyphenPos = strpos($serviceName, '-');
    if ($hyphenPos !== false) {
        // Find the last slash BEFORE the hyphen
        $lastSlashBeforeHyphen = strrpos(substr($serviceName, 0, $hyphenPos), '/');
        if ($lastSlashBeforeHyphen !== false) {
            // Take everything from that slash onwards
            $metricName = substr($serviceName, $lastSlashBeforeHyphen + 1);
        } else {
            // No slash before hyphen, take everything from hyphen onwards
            $metricName = substr($serviceName, $hyphenPos);
        }
    } else {
        // Fallback: if no hyphen found, use the original logic
        $metricName = substr($serviceName, strrpos($serviceName, '-') + 1);
    }
    
    // Create config name: azure_hostname__metricname
    $configName = 'azure_' . $hostName . '__' . sanitizeFilename($metricName);
    
    return <<<SERVICECFG
define service {
    #NAGIOSQL_CONFIG_NAME           {$configName}
    host_name                       {$hostName}
    service_description             {$metricName}
    check_command                   check_azure!{$resourceGroup}!{$hostName}!{$serviceName}!na!na
    max_check_attempts              3
    check_interval                  120
    retry_interval                  2
    active_checks_enabled           1
    check_period                    24x7
    flap_detection_enabled          1
    flap_detection_options          o,c
    process_perf_data               1
    retain_status_information       1
    notification_interval           60
    notification_period             24x7
    notifications_enabled           1
    contact_groups                  network-admins
    register                        1
}
SERVICECFG;
}

/**
 * Sanitize filename for config files
 */
function sanitizeFilename(string $filename): string
{
    return preg_replace('/[^a-zA-Z0-9_-]/', '_', $filename);
}

/**
 * Execute apmimport script
 */
function execApmImport(string $configFile): bool
{
    $command = "/usr/bin/sudo /usr/bin/apmimport " . escapeshellarg($configFile);
    exec($command, $output, $returnCode);
    return $returnCode === 0;
}

/**
 * Execute apmfinish script
 */
function execApmFinish(): bool
{
    $command = "/usr/bin/sudo /usr/bin/apmfinish";
    exec($command, $output, $returnCode);
    return $returnCode === 0;
}
