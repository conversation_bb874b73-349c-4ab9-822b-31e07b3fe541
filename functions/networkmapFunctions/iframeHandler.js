/**
 * <PERSON><PERSON><PERSON> Handler for Dashboards
 * Handles style injections to hide unwanted elements in iframes
 * and intercepts host links to use showModal
 */

class IframeHandler {
    constructor() {
        this.hostIPMap = null; // Cache for hostname to IP mapping
        this.init();
    }

    init() {
        // Handle main networkmap iframe
        this.handleMainIframe();
        
        // Handle modal iframes
        this.handleModalIframes();
    }

    /**
     * Fetch hostname to IP mapping from Nagios API
     * @returns {Promise<Object>} - Map of hostname to IP address
     */
    async fetchHostIPMapping() {
        if (this.hostIPMap !== null) {
            return this.hostIPMap;
        }
        
        try {
            const apiUrl = `https://${window.location.hostname}/nagios/cgi-bin/objectjson.cgi?query=hostlist&details=true`;
            const response = await fetch(apiUrl);
            
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.result.type_code !== 0) {
                throw new Error(`API error: ${data.result.message}`);
            }
            
            // Create hostname to IP mapping
            this.hostIPMap = {};
            for (const [hostName, hostObj] of Object.entries(data.data.hostlist)) {
                if (hostObj.address) {
                    this.hostIPMap[hostName] = hostObj.address;
                }
            }
            
            return this.hostIPMap;
        } catch (error) {
            console.error("Error fetching host IP mapping:", error);
            this.hostIPMap = {};
            return this.hostIPMap;
        }
    }

    /**
     * Get IP address for a given hostname
     * @param {string} hostname - The hostname to resolve
     * @returns {Promise<string>} - The IP address or hostname if not found
     */
    async getIPByHostname(hostname) {
        const hostIPMap = await this.fetchHostIPMapping();
        return hostIPMap[hostname] || hostname;
    }

    handleMainIframe() {
        const networkmapIframe = document.querySelector('.networkmap-iframe');
        if (networkmapIframe) {
            networkmapIframe.addEventListener('load', () => {
                this.injectStyles(networkmapIframe);
                this.interceptLinks(networkmapIframe);
            });
        }
    }

    handleModalIframes() {
        // Watch for modal iframe creation and handle existing ones
        const modalIframe = document.getElementById('modal-frame');
        if (modalIframe) {
            modalIframe.addEventListener('load', () => {
                this.injectStyles(modalIframe);
            });
        }

        // Also handle the showModal function override
        this.overrideShowModal();
    }

    interceptLinks(iframe) {
        try {
            const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;
            
            if (iframeDocument) {
                // Function to check if edit mode is active
                const isEditModeActive = () => {
                    const editIndicator = iframeDocument.getElementById('editIndicator');
                    return editIndicator && editIndicator.style.display !== 'none';
                };

                // Function to intercept Nagios links (both host status and service info)
                const interceptNagiosLinks = () => {
                    // Check if edit mode is active
                    const editModeActive = isEditModeActive();
                    
                    // Get all Nagios links
                    const allNagiosLinks = iframeDocument.querySelectorAll('a[href*="/status.cgi?host="], a[href*="/extinfo.cgi?type=2&host="]');
                    
                    if (editModeActive) {
                        return;
                    }

                    // Intercept host status links: /status.cgi?host=[host_name]
                    const hostStatusLinks = iframeDocument.querySelectorAll('a[href*="/status.cgi?host="]');
                    
                    hostStatusLinks.forEach(link => {
                        // Remove existing event listeners to prevent duplicates
                        link.removeEventListener('click', this.handleHostStatusLinkClick);
                        link.removeEventListener('click', this.preventDefaultHandler);
                        
                        // Add our custom click handler with async wrapper
                        link.addEventListener('click', async (event) => {
                            await this.handleHostStatusLinkClick(event);
                        });
                        
                        // Prevent default behavior
                        link.addEventListener('click', this.preventDefaultHandler);
                    });

                    // Intercept service info links: /extinfo.cgi?type=2&host=[host_name]&service=[service_description]
                    const serviceInfoLinks = iframeDocument.querySelectorAll('a[href*="/extinfo.cgi?type=2&host="]');
                    
                    serviceInfoLinks.forEach(link => {
                        // Remove existing event listeners to prevent duplicates
                        link.removeEventListener('click', this.handleServiceInfoLinkClick);
                        link.removeEventListener('click', this.preventDefaultHandler);
                        
                        // Add our custom click handler with async wrapper
                        link.addEventListener('click', async (event) => {
                            await this.handleServiceInfoLinkClick(event);
                        });
                        
                        // Prevent default behavior
                        link.addEventListener('click', this.preventDefaultHandler);
                    });
                };

                // Create a reusable preventDefault handler
                this.preventDefaultHandler = (e) => {
                    // Check if edit mode is active BEFORE preventing default
                    const iframe = e.target.closest('iframe') || document.querySelector('.networkmap-iframe');
                    if (iframe) {
                        const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;
                        const editIndicator = iframeDocument.getElementById('editIndicator');
                        const isEditModeActive = editIndicator && editIndicator.style.display !== 'none';
                        
                        if (isEditModeActive) {
                            return; // DO NOTHING - let the link work normally
                        }
                    }
                    
                    e.preventDefault();
                    e.stopPropagation();
                };

                // Initial interception
                interceptNagiosLinks();

                // Set up a mutation observer to watch for changes to the edit indicator
                const editIndicatorObserver = new MutationObserver((mutations) => {
                    mutations.forEach((mutation) => {
                        if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                            // Re-run link interception when edit mode changes
                            interceptNagiosLinks();
                        }
                    });
                });

                // Start observing the edit indicator for style changes
                const editIndicator = iframeDocument.getElementById('editIndicator');
                if (editIndicator) {
                    editIndicatorObserver.observe(editIndicator, {
                        attributes: true,
                        attributeFilter: ['style']
                    });
                }

                // Set up a mutation observer to handle dynamically added links
                const observer = new MutationObserver((mutations) => {
                    mutations.forEach((mutation) => {
                        if (mutation.type === 'childList') {
                            mutation.addedNodes.forEach((node) => {
                                if (node.nodeType === Node.ELEMENT_NODE) {
                                    // Check if edit mode is active - if so, DO NOTHING
                                    if (isEditModeActive()) {
                                        return;
                                    }

                                    // Check if the added node is a Nagios host status link
                                    if (node.tagName === 'A' && node.href && node.href.includes('/status.cgi?host=')) {
                                        node.addEventListener('click', async (event) => {
                                            await this.handleHostStatusLinkClick(event);
                                        });
                                        node.addEventListener('click', this.preventDefaultHandler);
                                    }
                                    
                                    // Check if the added node is a Nagios service info link
                                    if (node.tagName === 'A' && node.href && node.href.includes('/extinfo.cgi?type=2&host=')) {
                                        node.addEventListener('click', async (event) => {
                                            await this.handleServiceInfoLinkClick(event);
                                        });
                                        node.addEventListener('click', this.preventDefaultHandler);
                                    }
                                    
                                    // Check for Nagios links within the added node
                                    const hostStatusLinks = node.querySelectorAll ? node.querySelectorAll('a[href*="/status.cgi?host="]') : [];
                                    hostStatusLinks.forEach(link => {
                                        link.addEventListener('click', async (event) => {
                                            await this.handleHostStatusLinkClick(event);
                                        });
                                        link.addEventListener('click', this.preventDefaultHandler);
                                    });

                                    const serviceInfoLinks = node.querySelectorAll ? node.querySelectorAll('a[href*="/extinfo.cgi?type=2&host="]') : [];
                                    serviceInfoLinks.forEach(link => {
                                        link.addEventListener('click', async (event) => {
                                            await this.handleServiceInfoLinkClick(event);
                                        });
                                        link.addEventListener('click', this.preventDefaultHandler);
                                    });
                                }
                            });
                        }
                    });
                });

                // Start observing
                observer.observe(iframeDocument.body, {
                    childList: true,
                    subtree: true
                });

                console.log('IframeHandler: Nagios link interception set up successfully');
            }
        } catch (error) {
            console.warn('IframeHandler: Could not intercept links in iframe (CORS or other restriction):', error);
        }
    }

    async handleHostStatusLinkClick(event) {
        // Check if edit mode is active BEFORE doing anything
        const iframe = event.target.closest('iframe') || document.querySelector('.networkmap-iframe');
        if (iframe) {
            const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;
            const editIndicator = iframeDocument.getElementById('editIndicator');
            const isEditModeActive = editIndicator && editIndicator.style.display !== 'none';
            
            if (isEditModeActive) {
                return; // DO NOTHING - let the link work normally
            }
        }
        
        event.preventDefault();
        event.stopPropagation();
        
        const link = event.currentTarget;
        const href = link.href;
        
        // Extract the host parameter from the Nagios URL
        const url = new URL(href);
        const hostParam = url.searchParams.get('host');
        
        if (hostParam) {
            // Get the actual IP address for the hostname
            const actualIP = await this.getIPByHostname(hostParam);
            
            // Convert to bubblemaps host URL format with actual IP
            const bubblemapsUrl = `/bubblemaps/host.php?nickname=${encodeURIComponent(hostParam)}&ip=${encodeURIComponent(hostParam)}&infra=null&hostip=${encodeURIComponent(actualIP)}&subnet=External`;
            
            // Use the showModal function to open the host page
            if (typeof window.showModal === 'function') {
                window.showModal(bubblemapsUrl);
            } else {
                console.warn('IframeHandler: showModal function not available');
            }
        } else {
            console.warn('IframeHandler: No host parameter found in Nagios status URL');
        }
        
        return false;
    }

    async handleServiceInfoLinkClick(event) {
        // Check if edit mode is active BEFORE doing anything
        const iframe = event.target.closest('iframe') || document.querySelector('.networkmap-iframe');
        if (iframe) {
            const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;
            const editIndicator = iframeDocument.getElementById('editIndicator');
            const isEditModeActive = editIndicator && editIndicator.style.display !== 'none';
            
            if (isEditModeActive) {
                return; // DO NOTHING - let the link work normally
            }
        }
        
        event.preventDefault();
        event.stopPropagation();
        
        const link = event.currentTarget;
        const href = link.href;
        
        // Extract the host and service parameters from the Nagios URL
        const url = new URL(href);
        const hostParam = url.searchParams.get('host');
        const serviceParam = url.searchParams.get('service');
        
        if (hostParam && serviceParam) {
            // Get the actual IP address for the hostname
            const actualIP = await this.getIPByHostname(hostParam);
            
            // Convert to bubblemaps host URL format with actual IP
            const bubblemapsUrl = `/bubblemaps/host.php?nickname=${encodeURIComponent(hostParam)}&ip=${encodeURIComponent(hostParam)}&infra=null&hostip=${encodeURIComponent(actualIP)}&subnet=External`;
            
            // Use the showModal function to open the host page with service modal
            if (typeof window.showModal === 'function') {
                window.showModal(bubblemapsUrl, 'service', serviceParam);
            } else if (typeof window.openServiceInModal === 'function') {
                // Fallback to openServiceInModal if available
                window.openServiceInModal(hostParam, actualIP, 'External', serviceParam);
            } else {
                console.warn('IframeHandler: showModal or openServiceInModal function not available');
            }
        } else {
            console.warn('IframeHandler: Missing host or service parameter in Nagios service URL');
        }
        
        return false;
    }

    overrideShowModal() {
        // Store the original showModal function
        const originalShowModal = window.showModal;
        
        if (originalShowModal) {
            window.showModal = function(url) {
                // Call the original function
                originalShowModal(url);
                
                // Get the modal iframe and add our handler
                const modalIframe = document.getElementById('modal-frame');
                if (modalIframe) {
                    modalIframe.addEventListener('load', () => {
                        // Use setTimeout to ensure the iframe is fully loaded
                        setTimeout(() => {
                            this.injectStyles(modalIframe);
                        }, 100);
                    });
                }
            }.bind(this);
        }
    }

    injectStyles(iframe) {
        try {
            const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;
            
            if (iframeDocument) {
                // Create style element if it doesn't exist
                let styleElement = iframeDocument.getElementById('bubblemaps-injected-styles');
                
                if (!styleElement) {
                    styleElement = iframeDocument.createElement('style');
                    styleElement.id = 'bubblemaps-injected-styles';
                    iframeDocument.head.appendChild(styleElement);
                }

                // Add CSS to hide navbar and style the header
                const cssRules = `
                    /* Hide the navbar */
                    nav.navbar {
                        display: none !important;
                    }
                    
                    /* Hide the sidebar toggle */
                    #sidetoggle {
                        display: none !important;
                    }
                    
                    /* Hide the header toggle */
                    li.toggle {
                        display: none !important;
                    }
                    
                    /* Hide the language dropdown */
                    li.dropdown:has(#language-ddheader) {
                        display: none !important;
                    }
                    
                    /* Hide the original search link */
                    li:has(a[onclick*="showFrontendDialog"][onclick*="Search"]) {
                        display: none !important;
                    }
                    
                    /* Alternative selector for browsers that don't support :has() */
                    li a[onclick*="showFrontendDialog"][onclick*="Search"] {
                        display: none !important;
                    }
                    
                    /* Hide the entire li containing the search link */
                    li:has(a[onclick*="Search"]) {
                        display: none !important;
                    }
                    
                    /* Style the custom search bar - matching hostlist styles */
                    .custom-search-container {
                        display: inline-flex !important;
                        align-items: center !important;
                        gap: 8px !important;
                        margin-left: 15px !important;
                        background-color: transparent !important;
                        border-radius: 4px !important;
                        padding: 0 !important;
                        border: none !important;
                        position: relative !important;
                    }
                    
                    .custom-search-container input[type="text"] {
                        background-color: #333 !important;
                        border: none !important;
                        border-radius: 4px !important;
                        color: #ffffff !important;
                        padding: 4px 8px !important;
                        font-size: 12px !important;
                        height: 24px !important;
                        width: 160px !important;
                        outline: none !important;
                        box-sizing: border-box !important;
                    }
                    
                    .custom-search-container input[type="text"]:focus {
                        outline: none !important;
                        background-color: #444 !important;
                    }
                    
                    .custom-search-container input[type="text"]::placeholder {
                        color: #ccc !important;
                        font-size: 11px !important;
                    }
                    
                    .custom-search-container input[type="button"] {
                        background-color: #333 !important;
                        border: none !important;
                        border-radius: 4px !important;
                        color: #ccc !important;
                        padding: 4px 8px !important;
                        font-size: 12px !important;
                        height: 24px !important;
                        cursor: pointer !important;
                        transition: all 0.2s ease !important;
                        min-width: 60px !important;
                        box-sizing: border-box !important;
                    }
                    
                    .custom-search-container input[type="button"]:hover {
                        color: #fff !important;
                        background-color: #444 !important;
                    }
                    
                    /* Always hide statusMessage loading */
                    #statusMessage {
                        display: none !important;
                        visibility: hidden !important;
                        opacity: 0 !important;
                        pointer-events: none !important;
                    }
                    
                    /* Remove top positioning from background image */
                    #backgroundImage {
                        top: 0 !important;
                    }
                    
                    /* Style the header to match our theme */
                    #header {
                        background: #403c3c !important;
                        border-bottom: 1px solid #555 !important;
                        color: #ffffff !important;
                        left: 0 !important;
                    }
                    
                    #header .head li {
                        color: #ffffff !important;
                    }
                    
                    #header .head a {
                        color: #ffffff !important;
                        text-decoration: none !important;
                    }
                    
                    #header .dropdown span {
                        color: #ffffff !important;
                        cursor: pointer !important;
                    }
                    
                    /* Style dropdown menus when they are visible */
                    #header .dropdown-menu.show {
                        background: #403c3c !important;
                        border: 1px solid #555 !important;
                        color: #ffffff !important;
                    }
                    
                    #header .dropdown-menu.show a {
                        color: #ffffff !important;
                        padding: 8px 15px !important;
                    }
                    
                    /* Prevent text overflow in table elements within sections */
                    section > .mytable {
                        overflow: hidden !important;
                        word-wrap: break-word !important;
                        word-break: break-word !important;
                        width: 100% !important;
                        max-width: 100% !important;
                        box-sizing: border-box !important;
                    }
                    
                    section > .mytable td {
                        overflow: hidden !important;
                        word-wrap: break-word !important;
                        word-break: break-word !important;
                        max-width: 100% !important;
                        box-sizing: border-box !important;
                    }
                    
                    section > .mytable .tdlabel,
                    section > .mytable .tdbox,
                    section > .mytable .tdfield {
                        overflow: hidden !important;
                        word-wrap: break-word !important;
                        word-break: break-word !important;
                        max-width: 100% !important;
                        box-sizing: border-box !important;
                    }
                    
                    /* Ensure section contains its table properly */
                    section {
                        overflow: hidden !important;
                        width: 100% !important;
                        box-sizing: border-box !important;
                    }
                    
                    /* Force table to respect container boundaries */
                    .mytable {
                        table-layout: fixed !important;
                        width: 100% !important;
                        max-width: 100% !important;
                        overflow: hidden !important;
                    }
                    
                    /* Ensure table cells don't expand beyond container */
                    .mytable td {
                        max-width: 0 !important;
                        overflow: hidden !important;
                        word-wrap: break-word !important;
                        word-break: break-all !important;
                    }
                    
                    /* Label cells - keep text visible */
                    .mytable .tdlabel {
                        white-space: nowrap !important;
                        text-overflow: ellipsis !important;
                        overflow: hidden !important;
                        max-width: 30% !important;
                    }
                    
                    /* Checkbox cells - keep checkboxes visible */
                    .mytable .tdbox {
                        white-space: nowrap !important;
                        overflow: visible !important;
                        max-width: 10% !important;
                        text-align: center !important;
                    }
                    
                    /* Field cells - allow wrapping for long content */
                    .mytable .tdfield {
                        white-space: normal !important;
                        word-break: break-all !important;
                        max-width: 60% !important;
                        overflow: hidden !important;
                        text-overflow: ellipsis !important;
                    }
                    
                    /* Ensure the dialog/popup itself doesn't overflow */
                    .popupWindow {
                        max-width: 100vw !important;
                        overflow: hidden !important;
                        box-sizing: border-box !important;
                    }
                    
                    /* ===== HOVER TOOLTIP STYLES ===== */
                    /* Target hover elements with styling (no text colors) */
                    div[id*="-hover"].hover {
                        max-width: 400px !important;
                        max-height: 500px !important;
                        overflow-y: hidden !important; /* Hide scrollbar, we'll auto-scroll */
                        z-index: 10000 !important;
                        background: #ffffff !important;
                        border: 1px solid #ddd !important;
                        border-radius: 6px !important;
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
                        padding: 12px !important;
                        font-size: 12px !important;
                        line-height: 1.4 !important;
                        box-sizing: border-box !important;
                        scroll-behavior: smooth !important;
                    }
                    
                    /* Table styles within hover tooltips */
                    div[id*="-hover"].hover table {
                        width: 100% !important;
                        border-collapse: collapse !important;
                        margin: 0 !important;
                        font-size: 11px !important;
                    }
                    
                    div[id*="-hover"].hover table th {
                        background: #f5f5f5 !important;
                        padding: 6px 8px !important;
                        text-align: left !important;
                        font-weight: 600 !important;
                        border-bottom: 1px solid #ddd !important;
                    }
                    
                    div[id*="-hover"].hover table td {
                        padding: 4px 8px !important;
                        border-bottom: 1px solid #eee !important;
                        word-wrap: break-word !important;
                        word-break: break-word !important;
                        max-width: 200px !important;
                        overflow: hidden !important;
                        text-overflow: ellipsis !important;
                    }
                    
                    div[id*="-hover"].hover table td.label {
                        font-weight: 600 !important;
                        white-space: nowrap !important;
                        max-width: 120px !important;
                    }
                    
                    /* Child services table */
                    div[id*="-hover"].hover .childs {
                        margin-top: 8px !important;
                        border-top: 1px solid #ddd !important;
                        padding-top: 8px !important;
                    }
                    
                    div[id*="-hover"].hover .childs table {
                        font-size: 10px !important;
                    }
                    
                    div[id*="-hover"].hover .childs td {
                        padding: 2px 4px !important;
                        max-width: 150px !important;
                    }
                    
                    /* Responsive adjustments */
                    @media (max-width: 768px) {
                        div[id*="-hover"].hover {
                            max-width: 95vw !important;
                            max-height: 70vh !important;
                            font-size: 11px !important;
                            padding: 8px !important;
                        }
                        
                        div[id*="-hover"].hover table {
                            font-size: 10px !important;
                        }
                        
                        div[id*="-hover"].hover table td {
                            padding: 3px 6px !important;
                            max-width: 120px !important;
                        }
                    }
                    
                    /* Ensure tooltips are always visible */
                    div[id*="-hover"].hover {
                        display: block !important;
                        visibility: visible !important;
                        opacity: 1 !important;
                    }
                    
                    /* Override any inline styles that might interfere */
                    div[id*="-hover"].hover[style*="display: none"] {
                        display: block !important;
                    }
                    
                    div[id*="-hover"].hover[style*="visibility: hidden"] {
                        visibility: visible !important;
                    }
                    
                    div[id*="-hover"].hover[style*="opacity: 0"] {
                        opacity: 1 !important;
                    }
                    
                    /* Allow hiding when explicitly set */
                    div[id*="-hover"].hover.hidden,
                    div[id*="-hover"].hover[style*="display: none"]:not([data-force-show]) {
                        display: none !important;
                        visibility: hidden !important;
                        opacity: 0 !important;
                    }
                    
                    /* Transition for smooth show/hide */
                    div[id*="-hover"].hover {
                        transition: opacity 0.2s ease, visibility 0.2s ease !important;
                    }
                    
                    /* ===== MAPOBJ STYLES - THEME OVERRIDE ===== */
                    /* Target mapobj elements and their children */
                    .mapobj {
                        background: #ffffff !important;
                        border: 1px solid #e0e0e0 !important;
                        border-radius: 8px !important;
                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
                        padding: 15px !important;
                        margin: 8px !important;
                        transition: all 0.2s ease !important;
                        font-family: 'Calibri', sans-serif !important;
                        color: #333 !important;
                        text-decoration: none !important;
                        display: inline-block !important;
                        width: calc(33.333% - 16px) !important;
                        min-width: 250px !important;
                        max-width: 350px !important;
                        box-sizing: border-box !important;
                        vertical-align: top !important;
                    }
                    
                    .mapobj:hover {
                        background: #f8f8f8 !important;
                        border-color: #ccc !important;
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
                        transform: translateY(-1px) !important;
                    }
                    
                    /* Style the link inside mapobj */
                    .mapobj a {
                        text-decoration: none !important;
                        color: inherit !important;
                        display: flex !important;
                        align-items: center !important;
                        justify-content: center !important;
                        width: 100% !important;
                        height: 100% !important;
                        box-sizing: border-box !important;
                        text-align: center !important;
                    }
                    
                    /* Style the heading inside mapobj */
                    .mapobj h3 {
                        margin: 0 !important;
                        font-size: 18px !important;
                        font-weight: 700 !important;
                        color: #000 !important;
                        line-height: 1.2 !important;
                        text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
                        letter-spacing: 0.5px !important;
                        word-wrap: break-word !important;
                        overflow-wrap: break-word !important;
                        text-align: center !important;
                        flex: 1 !important;
                    }
                    
                    /* Style the state icon */
                    .mapobj .state {
                        float: none !important;
                        margin-left: 10px !important;
                        border-radius: 4px !important;
                        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
                        transition: transform 0.2s ease !important;
                        flex-shrink: 0 !important;
                    }
                    
                    .mapobj:hover .state {
                        transform: scale(1.05) !important;
                    }
                    
                    /* Status-specific styling for mapobj */
                    .mapobj[data-state="ok"] {
                        border-left: 4px solid #b4ca45 !important;
                        background: linear-gradient(135deg, #f0f9e8, #ffffff) !important;
                    }
                    
                    .mapobj[data-state="warning"] {
                        border-left: 4px solid #fec930 !important;
                        background: linear-gradient(135deg, #fff8e6, #ffffff) !important;
                    }
                    
                    .mapobj[data-state="down"],
                    .mapobj[data-state="critical"] {
                        border-left: 4px solid #d41d28 !important;
                        background: linear-gradient(135deg, #fde6e8, #ffffff) !important;
                    }
                    
                    .mapobj[data-state="unknown"] {
                        border-left: 4px solid #4a7fbe !important;
                        background: linear-gradient(135deg, #e6eef8, #ffffff) !important;
                    }
                    
                    .mapobj[data-state="pending"] {
                        border-left: 4px solid #808282 !important;
                        background: linear-gradient(135deg, #f2f2f2, #ffffff) !important;
                    }
                    
                    /* Container for mapobj elements - ensures proper column layout */
                    .mapobj-container {
                        display: flex !important;
                        flex-wrap: wrap !important;
                        gap: 16px !important;
                        justify-content: flex-start !important;
                        align-items: flex-start !important;
                        padding: 10px !important;
                    }
                    
                    /* Responsive adjustments for mapobj */
                    @media (max-width: 1200px) {
                        .mapobj {
                            width: calc(50% - 16px) !important;
                            min-width: 200px !important;
                        }
                    }
                    
                    @media (max-width: 768px) {
                        .mapobj {
                            width: calc(100% - 16px) !important;
                            min-width: auto !important;
                            max-width: none !important;
                            padding: 12px !important;
                            margin: 8px !important;
                        }
                        
                        .mapobj h3 {
                            font-size: 16px !important;
                        }
                        
                        .mapobj .state {
                            width: 18px !important;
                            height: 18px !important;
                        }
                        
                        .mapobj-container {
                            gap: 8px !important;
                            padding: 5px !important;
                        }
                    }
                    
                    @media (max-width: 480px) {
                        .mapobj {
                            padding: 10px !important;
                            margin: 5px !important;
                        }
                        
                        .mapobj h3 {
                            font-size: 14px !important;
                        }
                    }
                `;

                styleElement.textContent = cssRules;
                
                // Add JavaScript for smart hover tooltip positioning
                this.setupHoverTooltipPositioning(iframeDocument);
                
                // Add custom search bar functionality
                this.setupCustomSearchBar(iframeDocument);
                
                console.log('IframeHandler: Styles injected successfully');
            }
        } catch (error) {
            console.warn('IframeHandler: Could not inject styles into iframe (CORS or other restriction):', error);
        }
    }

    setupHoverTooltipPositioning(iframeDocument) {
        // Function to auto-scroll tooltip content continuously
        const autoScrollTooltip = (tooltip) => {
            if (!tooltip) return;
            
            // Additional check to ensure tooltip is still valid
            if (tooltip.style.display === 'none' || tooltip.style.visibility === 'hidden') {
                return;
            }
            
            const scrollHeight = tooltip.scrollHeight;
            const clientHeight = tooltip.clientHeight;
            
            // Only auto-scroll if content is taller than container
            if (scrollHeight > clientHeight) {
                const scrollDistance = scrollHeight - clientHeight;
                let isScrollingDown = true;
                let currentScroll = 0;
                
                // Reset scroll position to top
                tooltip.scrollTop = 0;
                
                // Create a unique identifier for this scroll session
                const scrollSessionId = Date.now() + Math.random();
                tooltip.currentScrollSession = scrollSessionId;
                
                // Continuous scroll function using setInterval for more reliable timing
                const startScrolling = () => {
                    // Check if this scroll session is still valid
                    if (!tooltip || tooltip.currentScrollSession !== scrollSessionId || 
                        tooltip.style.display === 'none' || tooltip.style.visibility === 'hidden') {
                        return;
                    }
                    
                    const scrollInterval = setInterval(() => {
                        // Check if tooltip still exists, is visible, and this session is still active
                        if (!tooltip || tooltip.currentScrollSession !== scrollSessionId || 
                            tooltip.style.display === 'none' || tooltip.style.visibility === 'hidden') {
                            clearInterval(scrollInterval);
                            return;
                        }
                        
                        const step = 2; // Medium step for medium scroll speed
                        
                        if (isScrollingDown) {
                            currentScroll += step;
                            if (currentScroll >= scrollDistance) {
                                currentScroll = scrollDistance;
                                isScrollingDown = false;
                            }
                        } else {
                            currentScroll -= step;
                            if (currentScroll <= 0) {
                                currentScroll = 0;
                                isScrollingDown = true;
                            }
                        }
                        
                        tooltip.scrollTop = currentScroll;
                    }, 30); // 30ms interval = medium scroll speed
                };
                
                // Start the continuous scroll after a brief delay
                setTimeout(startScrolling, 300);
            }
        };
        
        // Set up mutation observer to watch for hover tooltips
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        // Check if this is a hover tooltip
                        if (node.id && node.id.includes('-hover') && node.classList.contains('hover')) {
                            // Reset scroll position and invalidate any existing session
                            node.scrollTop = 0;
                            node.currentScrollSession = null;
                            
                            // Start auto-scroll after a brief delay
                            setTimeout(() => {
                                if (node && node.style.display !== 'none' && node.style.visibility !== 'hidden') {
                                    autoScrollTooltip(node);
                                }
                            }, 150);
                        }
                        
                        // Also check for hover tooltips within the added node
                        const hoverTooltips = node.querySelectorAll ? node.querySelectorAll('div[id*="-hover"].hover') : [];
                        hoverTooltips.forEach(tooltip => {
                            // Reset scroll position and invalidate any existing session
                            tooltip.scrollTop = 0;
                            tooltip.currentScrollSession = null;
                            
                            // Start auto-scroll after a brief delay
                            setTimeout(() => {
                                if (tooltip && tooltip.style.display !== 'none' && tooltip.style.visibility !== 'hidden') {
                                    autoScrollTooltip(tooltip);
                                }
                            }, 150);
                        });
                    }
                });
            });
        });
        
        // Start observing
        observer.observe(iframeDocument.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['style', 'class']
        });
        

        
        console.log('IframeHandler: Hover tooltip management setup complete');
    }

    setupCustomSearchBar(iframeDocument) {
        // Function to create and inject the custom search bar
        const createCustomSearchBar = () => {
            // Check if search bar already exists
            if (iframeDocument.querySelector('.custom-search-container')) {
                return;
            }

            // Check if we're on index.php without params - hide search bar
            try {
                const iframeWindow = iframeDocument.defaultView;
                const iframeLocation = iframeWindow.location;
                if (iframeLocation.pathname.includes('index.php') && iframeLocation.search === '') {
                    return; // Don't show search bar on index.php without params
                }
            } catch (e) {
                // If we can't access iframe location due to CORS, continue
                console.log('IframeHandler: Could not check iframe location, showing search bar');
            }

            // Find the Options dropdown element
            let optionsDropdown = iframeDocument.querySelector('li.dropdown:has(#wui-ddheader)');
            if (!optionsDropdown) {
                // Fallback for browsers that don't support :has()
                const wuiHeader = iframeDocument.querySelector('#wui-ddheader');
                if (wuiHeader) {
                    optionsDropdown = wuiHeader.closest('li.dropdown');
                }
            }
            if (!optionsDropdown) {
                return;
            }

            // Create the search bar container
            const searchContainer = iframeDocument.createElement('li');
            searchContainer.className = 'custom-search-container';
            searchContainer.style.cssText = 'display: inline-flex !important; align-items: center !important; gap: 8px !important; margin-left: 15px !important;';

            // Create the search form using the original HTML structure
            const searchForm = iframeDocument.createElement('div');
            searchForm.className = 'simple_form';
            searchForm.style.cssText = 'display: inline-flex !important; align-items: center !important; gap: 8px !important;';

            // Create the search input with original attributes
            const searchInput = iframeDocument.createElement('input');
            searchInput.type = 'text';
            searchInput.name = 'highlightInput';
            searchInput.id = 'highlightInput';
            searchInput.setAttribute('onkeypress', 'searchObjectsKeyCheck(this.value, event)');
            searchInput.setAttribute('autofocus', '');
            searchInput.placeholder = 'Search (case sensitive)...';
            searchInput.style.cssText = 'background-color: #333 !important; border: none !important; border-radius: 4px !important; color: #ffffff !important; padding: 4px 8px !important; font-size: 12px !important; height: 24px !important; width: 160px !important; outline: none !important;';

            // Create the search button with original attributes
            const searchButton = iframeDocument.createElement('input');
            searchButton.className = 'submit';
            searchButton.type = 'button';
            searchButton.name = 'submit';
            searchButton.value = 'Search';
            searchButton.setAttribute('onclick', 'searchObjects(document.getElementById("highlightInput").value)');
            searchButton.style.cssText = 'background-color: #333 !important; border: none !important; border-radius: 4px !important; color: #ccc !important; padding: 4px 8px !important; font-size: 12px !important; height: 24px !important; cursor: pointer !important; transition: all 0.2s ease !important; min-width: 60px !important;';

            // Append elements to form, then form to container
            searchForm.appendChild(searchInput);
            searchForm.appendChild(searchButton);
            searchContainer.appendChild(searchForm);

            // Insert the search bar after the Options dropdown
            optionsDropdown.parentNode.insertBefore(searchContainer, optionsDropdown.nextSibling);

            console.log('IframeHandler: Custom search bar added successfully');
        };

        // Function to hide the original search link
        const hideOriginalSearch = () => {
            // Find and hide the original search link
            const searchLinks = iframeDocument.querySelectorAll('a[onclick*="Search"]');
            searchLinks.forEach(link => {
                const listItem = link.closest('li');
                if (listItem) {
                    listItem.style.display = 'none';
                }
            });
        };

        // Try to create the search bar immediately
        createCustomSearchBar();
        hideOriginalSearch();

        // Set up a mutation observer to watch for the Options dropdown if it's not present yet
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // Check if the Options dropdown was added
                            if (node.querySelector && node.querySelector('li.dropdown:has(#wui-ddheader)')) {
                                createCustomSearchBar();
                            }
                            // Also check if the node itself is the Options dropdown
                            if (node.classList && node.classList.contains('dropdown') && node.querySelector('#wui-ddheader')) {
                                createCustomSearchBar();
                            }
                            // Check for search links to hide
                            if (node.querySelector && node.querySelector('a[onclick*="Search"]')) {
                                hideOriginalSearch();
                            }
                            // Also check if the node itself is a search link
                            if (node.tagName === 'A' && node.onclick && node.onclick.toString().includes('Search')) {
                                hideOriginalSearch();
                            }
                        }
                    });
                }
            });
        });

        // Start observing
        observer.observe(iframeDocument.body, {
            childList: true,
            subtree: true
        });

        // Also try again after a delay in case the elements load asynchronously
        setTimeout(() => {
            createCustomSearchBar();
            hideOriginalSearch();
        }, 1000);
        setTimeout(() => {
            createCustomSearchBar();
            hideOriginalSearch();
        }, 2000);
    }
}

// Initialize the iframe handler when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new IframeHandler();
});

// Also initialize immediately if DOM is already loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new IframeHandler();
    });
} else {
    new IframeHandler();
}
