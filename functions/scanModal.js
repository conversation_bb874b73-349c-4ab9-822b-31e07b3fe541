/**
 * Modular Scan Modal System
 * This file provides a reusable scan modal that can be used across all pages
 * (hosts.php, infra.php, subnets.php) with full Azure scan functionality.
 */

class ScanModal {
    constructor(options = {}) {
        this.modalId = options.modalId || 'scanModal';
        this.buttonId = options.buttonId || 'scanModal-button';
        this.formId = options.formId || 'scanForm';
        this.redirectUrl = options.redirectUrl || null;
        this.hasAzureSupport = options.hasAzureSupport !== false; // Default to true
        this.hasScanTypeSelector = options.hasScanTypeSelector !== false; // Default to true
        this.hasInternalCheckbox = options.hasInternalCheckbox !== false; // Default to true
        this.ipLabel = options.ipLabel || 'Please enter a single IP or IP range.';
        this.forceScan = options.forceScan !== false; // Default to true
        
        this.modal = null;
        this.form = null;
        this.azureWrapper = null;
        this.ipGroup = null;
        this.internalGroup = null;
        
        this.init();
    }
    
    init() {
        this.createModal();
        this.setupEventListeners();
        this.loadInfrastructureOptions();
    }
    
    createModal() {
        // Check if modal already exists
        if (document.getElementById(this.modalId)) {
            this.modal = document.getElementById(this.modalId);
            this.form = document.getElementById(this.formId);
            this.azureWrapper = document.querySelector('.azure-scan-wrapper');
            this.ipGroup = document.getElementById('ip')?.closest('.form-group');
            this.internalGroup = document.getElementById('internal')?.closest('.form-group');
            return;
        }
        
        // Create modal HTML
        const modalHTML = `
            <div id="${this.modalId}" class="formModal">
                <div class="formModal-content">
                    <span class="formModal-close">&times;</span>
                    <form class="modal-form" id="${this.formId}" action="scan.php" method="GET">
                        <div class="form-group">
                            <label for="infra">Add host(s) to:</label>
                            <div class="infra-select-wrapper">
                                <select id="infra" name="infra" class="form-control" required>
                                    <option value="">Loading...</option>
                                </select>
                                <button type="button" id="addInfraBtn" title="Add Infrastructure" class="add-infra-btn">
                                    <i class="fa fa-plus"></i>
                                </button>
                            </div>
                        </div>
                        ${this.hasScanTypeSelector ? `
                        <div class="form-group">
                            <label style="display:block;margin-bottom:4px;">Scan type:</label>
                            <label style="margin-right:10px;">
                                <input type="radio" name="scanType" value="network" checked> Network
                            </label>
                            <label>
                                <input type="radio" name="scanType" value="azure"> Azure
                            </label>
                        </div>
                        ` : ''}
                        <div class="form-group">
                            <label for="ip">${this.ipLabel}</label>
                            <input type="text" id="ip" name="ip" class="form-control" required>
                        </div>
                        ${this.hasInternalCheckbox ? `
                        <div class="form-group">
                            <label title="If checked, addresses outside RFC 1918 (e.g., 128.1.x.x) will be treated as internal and stored under their own subnet instead of 'External'.">
                                <input type="checkbox" id="internal" name="internal" value="yes"> Treat as internal (non-RFC1918)
                            </label>
                        </div>
                        ` : ''}
                        ${this.hasAzureSupport ? `
                        <div class="azure-scan-wrapper" style="display: none;">
                            <div class="form-group">
                                <label for="resourceGroup">Azure Resource Group</label>
                                <input type="text" id="resourceGroup" class="form-control" placeholder="e.g. MyResourceGroup" />
                            </div>
                            <div class="form-group">
                                <button type="button" id="btnListAzureResources" class="btn-submit">List Azure Resources</button>
                            </div>
                            <div class="form-group" id="azureResourcesContainer" style="max-height:230px;overflow:auto;"></div>
                            <div class="form-group" id="azureServicesContainer" style="max-height:230px;overflow:auto;"></div>
                            <div class="form-group" id="azureAddMonitoringContainer" style="display:none;">
                                <button type="button" id="btnAddAzureMonitoring" class="btn-submit">Add Monitoring</button>
                            </div>
                        </div>
                        ` : ''}
                        ${this.forceScan ? '<input type="hidden" name="forceScan" value="yes">' : ''}
                        <button type="submit" class="btn-submit">Submit</button>
                    </form>
                </div>
            </div>
        `;
        
        // Append modal to body
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        
        // Get references
        this.modal = document.getElementById(this.modalId);
        this.form = document.getElementById(this.formId);
        this.azureWrapper = document.querySelector('.azure-scan-wrapper');
        this.ipGroup = document.getElementById('ip')?.closest('.form-group');
        this.internalGroup = document.getElementById('internal')?.closest('.form-group');
    }
    
    setupEventListeners() {
        const span = this.modal.querySelector('.formModal-close');
        
        // Close modal on X click
        span.onclick = () => {
            this.hide();
        };
        
        // Close modal on outside click
        window.onclick = (event) => {
            if (event.target === this.modal) {
                this.hide();
            }
        };
        
        // Setup scan button click handler
        this.setupScanButton();
        
        // Setup form submission
        this.setupFormSubmission();
        
        // Setup Azure functionality if enabled
        if (this.hasAzureSupport) {
            this.setupAzureFunctionality();
        }
        
        // Setup Add Infrastructure button
        this.setupAddInfrastructureButton();
    }
    
    setupScanButton() {
        // Handle scan button clicks
        const scanButtons = [
            document.getElementById('scanModal-btn'),
            document.getElementById('formModal-button'),
            document.querySelector('[data-scan-modal]')
        ];
        
        scanButtons.forEach(btn => {
            if (btn) {
                btn.onclick = () => {
                    this.show();
                };
            }
        });
    }
    
    setupFormSubmission() {
        this.form.addEventListener('submit', async (event) => {
            event.preventDefault();
            
            const scanType = document.querySelector('input[name="scanType"]:checked')?.value;
            if (scanType === 'azure' && this.hasAzureSupport) {
                // Azure flow is handled by separate button, prevent form submission
                return;
            }
            
            const submitButton = this.form.querySelector('button[type="submit"]');
            submitButton.disabled = true;
            submitButton.textContent = 'Scanning...';
            
            try {
                const formData = new FormData(this.form);
                const params = new URLSearchParams(formData).toString();
                const infraValue = formData.get('infra');
                const input = formData.get('ip');
                
                if (!isValidNetworkString(input)) {
                    throw new Error('Invalid network string');
                }
                
                await fetch('scan.php?' + params);
                
                // Determine redirect URL
                let redirectUrl = this.redirectUrl;
                if (!redirectUrl) {
                    // Default redirect based on current page
                    const currentPage = window.location.pathname.split('/').pop();
                    if (currentPage === 'hosts.php') {
                        redirectUrl = `hosts.php?subnet=all&subnetNickname=All Hosts&infra=${infraValue}`;
                    } else if (currentPage === 'subnets.php') {
                        redirectUrl = `hosts.php?subnet=all&subnetNickname=All Hosts&infra=${infraValue}`;
                    } else if (currentPage === 'infra.php') {
                        redirectUrl = `hosts.php?subnet=all&subnetNickname=All Hosts&infra=${infraValue}`;
                    } else {
                        redirectUrl = `hosts.php?subnet=all&subnetNickname=All Hosts&infra=${infraValue}`;
                    }
                }
                
                window.location.href = redirectUrl;
            } catch (error) {
                alert(error.message);
            } finally {
                submitButton.disabled = false;
                submitButton.textContent = 'Submit';
            }
        });
    }
    
    setupAzureFunctionality() {
        // Toggle visibility based on scanType change
        this.form.addEventListener('change', (e) => {
            if (e.target.name === 'scanType') {
                const isAzure = e.target.value === 'azure';
                // Toggle groups
                if (this.ipGroup) this.ipGroup.style.display = isAzure ? 'none' : '';
                if (this.internalGroup) this.internalGroup.style.display = isAzure ? 'none' : '';
                if (this.azureWrapper) this.azureWrapper.style.display = isAzure ? '' : 'none';
                // Adjust required attribute for IP field
                const ipInput = document.getElementById('ip');
                if (ipInput) ipInput.required = !isAzure;
                // Hide/show submit button based on scan type
                const submitButton = this.form.querySelector('button[type="submit"]');
                if (submitButton) submitButton.style.display = isAzure ? 'none' : '';
            }
        });
        
        // Fetch list of Azure resources for given RG
        document.getElementById('btnListAzureResources').addEventListener('click', async () => {
            const rg = document.getElementById('resourceGroup').value.trim();
            if (!rg) {
                alert('Please enter a resource-group');
                return;
            }
            const resContainer = document.getElementById('azureResourcesContainer');
            const svcContainer = document.getElementById('azureServicesContainer');
            const addMonContainer = document.getElementById('azureAddMonitoringContainer');

            resContainer.innerHTML = 'Loading resources…';
            svcContainer.innerHTML = '';
            addMonContainer.style.display = 'none';

            try {
                const response = await fetch(`azure_scan.php?action=list_resources&rg=${encodeURIComponent(rg)}`);
                const data = await response.json();
                if (!data.success) throw new Error(data.message || 'Unknown error');
                if (!Array.isArray(data.resources) || data.resources.length === 0) {
                    resContainer.innerHTML = '<em>No resources found.</em>';
                    return;
                }
                // Build selectable list (radio buttons)
                resContainer.innerHTML = '<strong>Select a resource:</strong><br/>';
                data.resources.forEach(r => {
                    const line = document.createElement('div');
                    line.innerHTML = `<label><input type="radio" name="azureResource" value="${r}"> ${r}</label>`;
                    resContainer.appendChild(line);
                });
            } catch (err) {
                console.error(err);
                resContainer.innerHTML = `<span style="color:red;">Error: ${err.message}</span>`;
            }
        });

        // When a resource is picked => list its services
        document.getElementById('azureResourcesContainer').addEventListener('change', async (e) => {
            if (e.target.name !== 'azureResource') return;
            const rg = document.getElementById('resourceGroup').value.trim();
            const rn = e.target.value;
            const svcContainer = document.getElementById('azureServicesContainer');
            const addMonContainer = document.getElementById('azureAddMonitoringContainer');

            svcContainer.innerHTML = 'Loading services…';
            addMonContainer.style.display = 'none';

            try {
                const response = await fetch(`azure_scan.php?action=list_services&rg=${encodeURIComponent(rg)}&rn=${encodeURIComponent(rn)}`);
                const data = await response.json();
                if (!data.success) throw new Error(data.message || 'Unknown error');
                if (!Array.isArray(data.services) || data.services.length === 0) {
                    svcContainer.innerHTML = '<em>No services found.</em>';
                    return;
                }
                // Build list of checkboxes
                svcContainer.innerHTML = '<strong>Select services to monitor:</strong><br/>';
                data.services.forEach(s => {
                    const idSafe = s.replace(/[^a-zA-Z0-9_-]/g, '_');
                    const div = document.createElement('div');
                    div.innerHTML = `<label><input type="checkbox" name="azureService" value="${s}" checked> ${s}</label>`;
                    svcContainer.appendChild(div);
                });
                addMonContainer.style.display = '';
            } catch (err) {
                console.error(err);
                svcContainer.innerHTML = `<span style="color:red;">Error: ${err.message}</span>`;
            }
        });

        // Final step => send everything to backend to create host/services
        document.getElementById('btnAddAzureMonitoring').addEventListener('click', async () => {
            const rg = document.getElementById('resourceGroup').value.trim();
            const rn = document.querySelector('input[name="azureResource"]:checked')?.value;
            const serviceChecks = Array.from(document.querySelectorAll('input[name="azureService"]:checked')).map(ch => ch.value);
            const infraName = document.getElementById('infra').value;

            if (!rg || !rn) {
                alert('Please select a resource-group and a resource.');
                return;
            }
            if (serviceChecks.length === 0) {
                alert('Please select at least one service to monitor.');
                return;
            }
            if (!infraName) {
                alert('Please select an infrastructure.');
                return;
            }

            // Convert infrastructure name to ID for Azure scan
            const infraId = await this.getInfrastructureId(infraName);

            const btn = document.getElementById('btnAddAzureMonitoring');
            btn.disabled = true;
            const originalText = btn.textContent;
            btn.textContent = 'Adding…';

            try {
                const response = await fetch('azure_scan.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'add_monitoring',
                        rg,
                        rn,
                        services: serviceChecks,
                        infra: infraId
                    })
                });
                const data = await response.json();
                if (data.success) {
                    alert('Monitoring successfully added.');
                    // refresh page to reflect new host (optional)
                    window.location.reload();
                } else {
                    throw new Error(data.message || 'Operation failed');
                }
            } catch (err) {
                alert('Error: ' + err.message);
            } finally {
                btn.disabled = false;
                btn.textContent = originalText;
            }
        });
    }
    
    setupAddInfrastructureButton() {
        const addInfraBtn = document.getElementById('addInfraBtn');
        if (addInfraBtn) {
            // Remove existing listener to avoid duplicates
            addInfraBtn.removeEventListener('click', this.handleAddInfraClick);
            addInfraBtn.addEventListener('click', this.handleAddInfraClick);
        }
    }
    
    handleAddInfraClick = () => {
        this.showAddInfrastructureDialog();
    }
    
    showAddInfrastructureDialog() {
        const infraName = prompt('Enter infrastructure name:');
        if (infraName && infraName.trim()) {
            this.addInfrastructure(infraName.trim());
        }
    }
    
    async addInfrastructure(name) {
        try {
            const formData = new FormData();
            formData.append('name', name);
            
            const response = await fetch('add_infra.php', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.text();
            
            // Check if the response contains success message
            if (result.includes('alert-success')) {
                alert('Infrastructure added successfully!');
                // Reload infrastructure options
                await this.loadInfrastructureOptions();
                // Select the newly added infrastructure
                const select = document.getElementById('infra');
                if (select) {
                    select.value = name;
                }
            } else {
                // Extract error message from the response
                const errorMatch = result.match(/<div class="alert alert-danger">([^<]+)<\/div>/);
                const errorMessage = errorMatch ? errorMatch[1] : 'Failed to add infrastructure';
                alert(errorMessage);
            }
        } catch (error) {
            console.error('Error adding infrastructure:', error);
            alert('Error adding infrastructure: ' + error.message);
        }
    }
    
    async loadInfrastructureOptions() {
        try {
            const response = await fetch('get_infra_name.php');
            const infras = await response.json();
            
            const select = document.getElementById('infra');
            select.innerHTML = '<option value="">Select Infrastructure</option>';
            
            infras.forEach(infra => {
                const option = document.createElement('option');
                option.value = infra.name; // Use name as value for network scans
                option.textContent = infra.name; // Use name as display text
                select.appendChild(option);
            });
        } catch (error) {
            console.error('Error loading infrastructure options:', error);
        }
    }
    
    show() {
        this.modal.style.display = 'block';
        this.modal.classList.add('show');
        // Ensure the Add Infrastructure button is set up
        this.setupAddInfrastructureButton();
    }
    
    hide() {
        this.modal.style.display = 'none';
        this.modal.classList.remove('show');
    }
    
    // Network string validation - Uses global isValidNetworkString from helperFunctions.js
    
    async getInfrastructureName(infraId) {
        try {
            const response = await fetch('get_infra_name.php');
            const infras = await response.json();
            
            const infra = infras.find(i => i.id == infraId);
            return infra ? infra.name : infraId; // Fallback to ID if not found
        } catch (error) {
            console.error('Error getting infrastructure name:', error);
            return infraId; // Fallback to ID if error
        }
    }
    
    async getInfrastructureId(infraName) {
        try {
            const response = await fetch('get_infra_name.php');
            const infras = await response.json();
            
            const infra = infras.find(i => i.name === infraName);
            return infra ? infra.id : infraName; // Fallback to name if not found
        } catch (error) {
            console.error('Error getting infrastructure ID:', error);
            return infraName; // Fallback to name if error
        }
    }
}

// Global function to create scan modal (for backward compatibility)
function createScanModal(options = {}) {
    return new ScanModal(options);
}

// Auto-initialize if DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        // Auto-detect page type and create appropriate modal
        const currentPage = window.location.pathname.split('/').pop();
        
        if (currentPage === 'hosts.php') {
            // Hosts page - full modal with Azure support
            window.scanModal = new ScanModal({
                modalId: 'formModal',
                hasAzureSupport: true,
                hasScanTypeSelector: true,
                hasInternalCheckbox: true,
                ipLabel: 'Enter a single ip, ip range or url.',
                forceScan: true
            });
        } else if (currentPage === 'subnets.php') {
            // Subnets page - full modal with Azure support
            window.scanModal = new ScanModal({
                modalId: 'formModal',
                hasAzureSupport: true,
                hasScanTypeSelector: true,
                hasInternalCheckbox: true,
                forceScan: true
            });
        } else if (currentPage === 'infra.php') {
            // Infra page - full modal with Azure support
            window.scanModal = new ScanModal({
                modalId: 'scanModal',
                hasAzureSupport: true,
                hasScanTypeSelector: true,
                hasInternalCheckbox: true,
                forceScan: true
            });
        }
    });
} else {
    // DOM is already ready
    const currentPage = window.location.pathname.split('/').pop();
    
    if (currentPage === 'hosts.php') {
        window.scanModal = new ScanModal({
            modalId: 'formModal',
            hasAzureSupport: true,
            hasScanTypeSelector: true,
            hasInternalCheckbox: true,
            ipLabel: 'Enter a single ip, ip range or url.',
            forceScan: true
        });
    } else if (currentPage === 'subnets.php') {
        window.scanModal = new ScanModal({
            modalId: 'formModal',
            hasAzureSupport: true,
            hasScanTypeSelector: true,
            hasInternalCheckbox: true,
            forceScan: true
        });
    } else if (currentPage === 'infra.php') {
        window.scanModal = new ScanModal({
            modalId: 'scanModal',
            hasAzureSupport: true,
            hasScanTypeSelector: true,
            hasInternalCheckbox: true,
            forceScan: true
        });
    }
} 