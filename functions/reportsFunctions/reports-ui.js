(function() {
    // UI Elements
    let startInput, endInput, typeSel, hostgroupSel, serviceDescriptionSel, saveBtn, scheduleBtn, viewSavedBtn;
    let contentDiv, statusFilterContainer, srDisableBtn, srStatus;

    // Status options & state
    const hostStatusOptions = [
        { key: 'up', label: 'Up', class: 'ok' },
        { key: 'down', label: 'Down', class: 'critical' },
        { key: 'unreachable', label: 'Unreach', class: 'unknown' }
    ];

    const serviceStatusOptions = [
        { key: 'ok', label: 'OK', class: 'ok' },
        { key: 'warning', label: 'Warn', class: 'warning' },
        { key: 'critical', label: 'Crit', class: 'critical' },
        { key: 'unknown', label: 'Unk', class: 'unknown' }
    ];

    let activeStatuses = new Set();

    // Initialize UI elements
    function initializeElements() {
        startInput = document.getElementById('report-start');
        endInput = document.getElementById('report-end');
        typeSel = document.getElementById('report-type');
        hostgroupSel = document.getElementById('report-hostgroup');
        serviceDescriptionSel = document.getElementById('report-service-description');
        saveBtn = document.getElementById('report-save');
        scheduleBtn = document.getElementById('report-schedule');
        viewSavedBtn = document.getElementById('view-saved-reports');
        contentDiv = document.getElementById('reports-content');
        statusFilterContainer = document.getElementById('reports-status-filters');
        srDisableBtn = document.getElementById('sr-disable');
        srStatus = document.getElementById('sr-status');
    }

    // Initialize date pickers using server time if possible
    function initDates() {
        return new Promise((resolve) => {
            const progUrl = '/nagios/cgi-bin/statusjson.cgi?query=programstatus';
            fetch(progUrl, { credentials: 'include' })
                .then(r => r.json())
                .then(js => js?.data?.programstatus?.current_time)
                .then(serverSec => {
                    const endDate = serverSec ? new Date(serverSec * 1000) : new Date();
                    endInput.value = window.reportsCore.toLocalIso(endDate);
                    startInput.value = window.reportsCore.toLocalIso(new Date(endDate.getTime() - 24 * 60 * 60 * 1000));
                    resolve();
                })
                .catch(() => {
                    const now = new Date();
                    endInput.value = window.reportsCore.toLocalIso(now);
                    startInput.value = window.reportsCore.toLocalIso(new Date(now.getTime() - 24 * 60 * 60 * 1000));
                    resolve();
                });
        });
    }

    // ------------------ Dropdown population helpers ------------------

    // Remove all options except the first ("All")
    function resetSelectOptions(sel) {
        while (sel.options.length > 1) sel.remove(1);
    }

    // Generic helper to append unique option values keeping "All" first
    function appendOptions(arr) {
        const existing = new Set(Array.from(hostgroupSel.options).map(o => o.value));
        arr.sort().forEach(v => {
            if (!existing.has(v)) {
                const opt = document.createElement('option');
                opt.value = v;
                opt.textContent = v;
                hostgroupSel.appendChild(opt);
            }
        });
    }

    // Populate hostgroups dropdown using availability endpoint only
    function populateHostgroups() {
        resetSelectOptions(hostgroupSel);
        const nowSec = Math.floor(Date.now() / 1000);
        const url = `/nagios/cgi-bin/archivejson.cgi?query=availability&availabilityobjecttype=hostgroups&starttime=${nowSec - 3600}&endtime=${nowSec}`;
        fetch(url, { credentials: 'include' })
            .then(r => (r.ok ? r.json() : Promise.reject(r.status)))
            .then(js => {
                const arr = js?.data?.hostgroups?.map(g => g.name) || (js?.data?.hostgroup ? [js.data.hostgroup.name] : []);
                if (arr.length) {
                    appendOptions(arr);
                } else if (typeof fetchAllHostGroups === 'function') {
                    // ultimate fallback DB
                    fetchAllHostGroups().then(appendOptions);
                }
            })
            .catch(() => {
                if (typeof fetchAllHostGroups === 'function') fetchAllHostGroups().then(appendOptions);
            });
    }

    // Populate hosts dropdown using availability endpoint
    function populateHosts() {
        resetSelectOptions(hostgroupSel);
        const nowSec = Math.floor(Date.now() / 1000);
        const url = `/nagios/cgi-bin/archivejson.cgi?query=availability&availabilityobjecttype=hosts&starttime=${nowSec - 3600}&endtime=${nowSec}`;
        fetch(url, { credentials: 'include' })
            .then(r => (r.ok ? r.json() : Promise.reject(r.status)))
            .then(js => {
                const arr = js?.data?.hosts?.map(h => h.name) || (js?.data?.host ? [js.data.host.name] : []);
                if (arr.length) {
                    appendOptions(arr);
                } else if (typeof fetchAllHosts === 'function') {
                    // If there's a helper available for full host list
                    fetchAllHosts().then(appendOptions);
                }
            })
            .catch(() => {
                if (typeof fetchAllHosts === 'function') fetchAllHosts().then(appendOptions);
            });
    }

    // Populate service descriptions dropdown
    async function populateServiceDescriptions(hostname = '') {
        resetSelectOptions(serviceDescriptionSel);
        
        try {
            let url = '/nagios/cgi-bin/objectjson.cgi?query=servicelist';
            if (hostname && hostname !== 'all') {
                url += '&hostname=' + encodeURIComponent(hostname);
            }
            
            const response = await fetch(url, { credentials: 'include' });
            if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
            
            const data = await response.json();
            const servicelist = data?.data?.servicelist || {};
            
            // Collect all unique service descriptions
            const serviceDescriptions = new Set();
            Object.values(servicelist).forEach(services => {
                if (Array.isArray(services)) {
                    services.forEach(service => serviceDescriptions.add(service));
                }
            });
            
            // Sort and add to dropdown
            const sortedServices = Array.from(serviceDescriptions).sort();
            sortedServices.forEach(service => {
                const opt = document.createElement('option');
                opt.value = service;
                opt.textContent = service;
                serviceDescriptionSel.appendChild(opt);
            });
        } catch (error) {
            console.error('Error fetching service descriptions:', error);
        }
    }

    // Update dropdown + label based on selected report type
    function refreshEntityDropdown() {
        // Since we moved the controls to the modal, this function is no longer needed
        // but we keep it for compatibility
        return;
    }

    // Build status filters
    function buildStatusFilters(type){
        statusFilterContainer.innerHTML = '';
        const opts = type === 'services' ? serviceStatusOptions : hostStatusOptions;
        activeStatuses = new Set(opts.map(o=>o.key));
        opts.forEach(o=>{
            const btn = document.createElement('button');
            btn.className = `reports-status-filter ${o.class} active`;
            btn.dataset.statusKey = o.key;
            btn.title = o.label;
            btn.textContent = o.label;
            btn.addEventListener('click', ()=>{
                if (btn.classList.contains('active')){
                    btn.classList.remove('active');
                    activeStatuses.delete(o.key);
                } else {
                    btn.classList.add('active');
                    activeStatuses.add(o.key);
                }
                window.reportsFilters.applyStatusFilter();
            });
            statusFilterContainer.appendChild(btn);
        });
    }

    // UI State Management
    function showLoading() {
        contentDiv.innerHTML = '<div class="reports-loading"><i class="fa fa-spinner fa-spin"></i> Loading...</div>';
    }

    function showError(message) {
        contentDiv.innerHTML = `<div class="reports-error">${message}</div>`;
    }

    function showEmpty(message) {
        contentDiv.innerHTML = `<div class="reports-empty">${message}</div>`;
    }

    // Getter methods for external access
    function getSelectedType() {
        return typeSel.value;
    }

    function getStartInput() {
        return startInput;
    }

    function getEndInput() {
        return endInput;
    }

    function getHostgroupValue() {
        return hostgroupSel.value;
    }

    function getServiceDescriptionValue() {
        return serviceDescriptionSel ? serviceDescriptionSel.value : 'all';
    }

    function getActiveStatuses() {
        return activeStatuses;
    }

    function getSearchTerm() {
        const searchInput = document.getElementById('report-search');
        return searchInput ? searchInput.value : '';
    }

    // Event Handlers
    function setupEventListeners() {
        // Attach handlers
        if(saveBtn) saveBtn.addEventListener('click', window.reportsSave.saveReport);
        if(viewSavedBtn) viewSavedBtn.addEventListener('click', window.reportsSave.openSavedReportsModal);
        
        // Search input with debouncing
        const searchInput = document.getElementById('report-search');
        const searchClearBtn = document.getElementById('report-search-clear');
        
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    window.reportsCore.generateReport();
                }, 300); // 300ms delay for better performance
                
                // Show/hide clear button based on input value
                updateClearButtonVisibility();
            });
            
            // Clear button functionality
            if (searchClearBtn) {
                searchClearBtn.addEventListener('click', () => {
                    searchInput.value = '';
                    searchInput.focus();
                    updateClearButtonVisibility();
                    window.reportsCore.generateReport();
                });
            }
            
            // Function to update clear button visibility
            function updateClearButtonVisibility() {
                if (searchClearBtn) {
                    if (searchInput.value.trim() !== '') {
                        searchClearBtn.classList.add('visible');
                    } else {
                        searchClearBtn.classList.remove('visible');
                    }
                }
            }
            
            // Initial visibility check
            updateClearButtonVisibility();
        }
        
        // Schedule button handler
        if(scheduleBtn){
            scheduleBtn.addEventListener('click', ()=>{
                const scheduleModal = document.getElementById('scheduleReportModal');
                if(scheduleModal) {
                    scheduleModal.style.display = 'flex';
                    // If the scheduledReportsUI is available, use it to load reports
                    if(window.scheduledReportsUI && window.scheduledReportsUI.loadScheduledReports) {
                        window.scheduledReportsUI.loadScheduledReports();
                    } else {
                        // Show a basic message if the module isn't loaded
                        const reportsList = document.getElementById('scheduled-reports-list');
                        if(reportsList) {
                            reportsList.innerHTML = '<div class="scheduled-reports-empty">Scheduled reports system loading...</div>';
                        }
                    }
                } else {
                    alert('Modal not found - please refresh the page');
                }
            });
        }

        // Scheduled reports search functionality
        const scheduledReportsSearchInput = document.getElementById('scheduled-reports-search-input');
        const scheduledReportsSearchClear = document.getElementById('scheduled-reports-search-clear');
        
        if (scheduledReportsSearchInput) {
            let searchTimeout;
            scheduledReportsSearchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    if (window.scheduledReportsUI && window.scheduledReportsUI.displayScheduledReports) {
                        window.scheduledReportsUI.displayScheduledReports();
                    }
                }, 300); // 300ms delay for better performance
                
                // Show/hide clear button based on input value
                updateScheduledReportsClearButtonVisibility();
            });
            
            // Clear button functionality
            if (scheduledReportsSearchClear) {
                scheduledReportsSearchClear.addEventListener('click', () => {
                    scheduledReportsSearchInput.value = '';
                    scheduledReportsSearchInput.focus();
                    updateScheduledReportsClearButtonVisibility();
                    if (window.scheduledReportsUI && window.scheduledReportsUI.displayScheduledReports) {
                        window.scheduledReportsUI.displayScheduledReports();
                    }
                });
            }
            
            // Function to update clear button visibility
            function updateScheduledReportsClearButtonVisibility() {
                if (scheduledReportsSearchClear) {
                    if (scheduledReportsSearchInput.value.trim() !== '') {
                        scheduledReportsSearchClear.classList.add('visible');
                    } else {
                        scheduledReportsSearchClear.classList.remove('visible');
                    }
                }
            }
            
            // Initial visibility check
            updateScheduledReportsClearButtonVisibility();
        }

        // Note: Type and hostgroup change handlers are now handled by the filters modal

        // Setup modal handlers
        setupModalHandlers();
    }

    function setupModalHandlers() {
        const scheduleModal = document.getElementById('scheduleReportModal');
        const savedReportsModal = document.getElementById('savedReportsModal');
        const saveReportNameModal = document.getElementById('saveReportNameModal');
        const srClose = document.querySelectorAll('.sr-close');
        const srCancel = document.getElementById('sr-cancel');
        const srForm = document.getElementById('schedule-form');
        const addNewReportBtn = document.getElementById('add-new-report-btn');
        const addReportForm = document.getElementById('add-report-form');
        const saveReportForm = document.getElementById('save-report-form');
        const saveReportCancel = document.getElementById('save-report-cancel');

        // Close modal handlers
        srClose.forEach(closeBtn => {
            closeBtn.addEventListener('click', () => {
                const modal = closeBtn.closest('.sr-modal');
                if (modal) {
                    // Check if we're in the schedule report modal and handle forms
                    if (modal.id === 'scheduleReportModal') {
                        handleScheduleModalClose();
                    } else {
                        modal.style.display = 'none';
                    }
                }
            });
        });
        
        // Click outside modal to close
        document.addEventListener('click', (event) => {
            const modals = document.querySelectorAll('.sr-modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    // Check if we're in the schedule report modal and handle forms
                    if (modal.id === 'scheduleReportModal') {
                        handleScheduleModalClose();
                    } else {
                        modal.style.display = 'none';
                    }
                }
            });
        });
        
        // Handle schedule modal close (X button or clicking outside)
        function handleScheduleModalClose() {
            const reportsList = document.getElementById('scheduled-reports-list');
            const addForm = document.getElementById('add-report-form');
            const editForm = document.getElementById('edit-report-form');
            const addBtn = document.getElementById('add-new-report-btn');
            const searchBar = document.querySelector('.scheduled-reports-search');
            const addServiceDescriptionField = document.getElementById('sr-service-description-field');
            const editServiceDescriptionField = document.getElementById('edit-sr-service-description-field');
            
            // Check which form is currently visible and handle accordingly
            if (addForm && addForm.style.display !== 'none') {
                // Add form is visible - cancel it
                if (reportsList && addForm && addBtn) {
                    reportsList.style.display = 'block';
                    addForm.style.display = 'none';
                    addBtn.style.display = 'inline-block';
                    if (searchBar) searchBar.style.display = 'block';
                    if (addServiceDescriptionField) addServiceDescriptionField.style.display = 'none';
                }
            } else if (editForm && editForm.style.display !== 'none') {
                // Edit form is visible - cancel it
                if (reportsList && editForm && addForm && addBtn) {
                    reportsList.style.display = 'block';
                    editForm.style.display = 'none';
                    addForm.style.display = 'none';
                    addBtn.style.display = 'inline-block';
                    if (searchBar) searchBar.style.display = 'block';
                    if (editServiceDescriptionField) editServiceDescriptionField.style.display = 'none';
                }
            } else {
                // No form is visible - close the entire modal
                const modal = document.getElementById('scheduleReportModal');
                if (modal) modal.style.display = 'none';
            }
        }

        // Add new report button handler
        if(addNewReportBtn){
            addNewReportBtn.addEventListener('click', ()=>{
                const reportsList = document.getElementById('scheduled-reports-list');
                const form = document.getElementById('add-report-form');
                const searchBar = document.querySelector('.scheduled-reports-search');
                if(reportsList && form){
                    reportsList.style.display = 'none';
                    form.style.display = 'block';
                    addNewReportBtn.style.display = 'none';
                    if(searchBar) searchBar.style.display = 'none';
                    
                    // Reset form
                    document.getElementById('sr-name').value = '';
                    document.getElementById('sr-email').value = '';
                    document.getElementById('sr-frequency').value = 'daily';
                    document.getElementById('sr-time').value = '00:05';
                    document.getElementById('sr-range').value = 1;
                    document.getElementById('sr-type').value = 'hostgroups,services';
                    document.getElementById('sr-entity').value = '';
                    
                    // Reset status checkboxes
                    document.querySelectorAll('#sr-host-statuses input').forEach(cb => cb.checked = true);
                    document.querySelectorAll('#sr-svc-statuses input').forEach(cb => cb.checked = true);
                    document.getElementById('sr-save-to-server').checked = true;
                    
                    // Set initial label and populate dropdowns
                    const entityLabel = document.querySelector('label[for="sr-entity"]');
                    const serviceDescriptionField = document.getElementById('sr-service-description-field');
                    if (entityLabel) {
                        entityLabel.textContent = 'Hostgroup (optional)';
                    }
                    if (serviceDescriptionField) {
                        serviceDescriptionField.style.display = 'none';
                    }
                    populateScheduledReportDropdowns();
                }
            });
        }
        
        if(srCancel){
            srCancel.addEventListener('click', ()=>{
                const reportsList = document.getElementById('scheduled-reports-list');
                const form = document.getElementById('add-report-form');
                const addBtn = document.getElementById('add-new-report-btn');
                const searchBar = document.querySelector('.scheduled-reports-search');
                const serviceDescriptionField = document.getElementById('sr-service-description-field');
                
                if(reportsList && form && addBtn){
                    reportsList.style.display = 'block';
                    form.style.display = 'none';
                    addBtn.style.display = 'inline-block';
                    if(searchBar) searchBar.style.display = 'block';
                    if(serviceDescriptionField) serviceDescriptionField.style.display = 'none';
                }
            });
        }
        
        // Save report name modal handlers
        if(saveReportCancel){
            saveReportCancel.addEventListener('click', ()=>{
                if(saveReportNameModal) saveReportNameModal.style.display='none';
            });
        }
        
        if(saveReportForm){
            saveReportForm.addEventListener('submit', function(e){
                e.preventDefault();
                const reportName = document.getElementById('report-name').value.trim();
                window.reportsSave.handleSaveReportForm(reportName);
            });
        }

        // Edit scheduled report form handlers
        const editSrCancel = document.getElementById('edit-sr-cancel');
        const editSrForm = document.getElementById('edit-schedule-form');
        const editSrTypeSelect = document.getElementById('edit-sr-type');

        if(editSrCancel){
            editSrCancel.addEventListener('click', ()=>{
                const reportsList = document.getElementById('scheduled-reports-list');
                const editForm = document.getElementById('edit-report-form');
                const addForm = document.getElementById('add-report-form');
                const addBtn = document.getElementById('add-new-report-btn');
                const searchBar = document.querySelector('.scheduled-reports-search');
                const serviceDescriptionField = document.getElementById('edit-sr-service-description-field');
                
                if(reportsList && editForm && addForm && addBtn){
                    reportsList.style.display = 'block';
                    editForm.style.display = 'none';
                    addForm.style.display = 'none';
                    addBtn.style.display = 'inline-block';
                    if(searchBar) searchBar.style.display = 'block';
                    if(serviceDescriptionField) serviceDescriptionField.style.display = 'none';
                }
            });
        }

        // Handle edit report type change
        if (editSrTypeSelect) {
            editSrTypeSelect.addEventListener('change', function() {
                // Update the label based on report type
                const entityLabel = document.querySelector('label[for="edit-sr-entity"]');
                const serviceDescriptionField = document.getElementById('edit-sr-service-description-field');
                if (entityLabel) {
                    entityLabel.textContent = this.value === 'services' ? 'Host (optional)' : 'Hostgroup (optional)';
                }
                
                // Show/hide service description field
                if (serviceDescriptionField) {
                    serviceDescriptionField.style.display = this.value === 'services' ? 'block' : 'none';
                }
                
                // Repopulate the dropdown with the appropriate data
                if (window.scheduledReportsUI && window.scheduledReportsUI.populateEditScheduledReportDropdowns) {
                    window.scheduledReportsUI.populateEditScheduledReportDropdowns();
                }
            });
        }

        // Handle edit entity change for service descriptions
        const editSrEntitySelect = document.getElementById('edit-sr-entity');
        if (editSrEntitySelect) {
            editSrEntitySelect.addEventListener('change', function() {
                const reportType = document.getElementById('edit-sr-type').value;
                if (reportType === 'services') {
                    const serviceDescriptionSelect = document.getElementById('edit-sr-service-description');
                    if (serviceDescriptionSelect) {
                        populateScheduledReportServiceDescriptions(serviceDescriptionSelect, this.value);
                    }
                }
            });
        }

        if(editSrForm){
            editSrForm.addEventListener('submit', async function(e){
                e.preventDefault();
                const originalName = document.getElementById('edit-sr-original-name').value.trim();
                const name = document.getElementById('edit-sr-name').value.trim();
                const email = document.getElementById('edit-sr-email').value.trim();
                const freq = document.getElementById('edit-sr-frequency').value;
                const time = document.getElementById('edit-sr-time').value || '00:05';
                const range = parseInt(document.getElementById('edit-sr-range').value, 10) || 1;
                const type = document.getElementById('edit-sr-type').value;
                const entity = document.getElementById('edit-sr-entity').value;

                // Validate required fields
                if (!name || !email) {
                    alert('Please provide a report name and email address.');
                    return;
                }

                // Validate multiple emails separated by semicolons
                const emails = email.split(';').map(e => e.trim()).filter(e => e.length > 0);
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                for (const singleEmail of emails) {
                    if (!emailRegex.test(singleEmail)) {
                        alert('Please provide valid email address(es). Invalid email: ' + singleEmail);
                        return;
                    }
                }
                if (emails.length === 0) {
                    alert('Please provide at least one valid email address.');
                    return;
                }

                const params = new URLSearchParams();
                params.append('action', 'update');
                params.append('originalName', originalName);
                params.append('name', name);
                params.append('email', email);
                params.append('frequency', freq);
                params.append('time', time);
                params.append('range', range);
                params.append('type', type);
                
                // Add day selection parameters
                if (freq === 'weekly') {
                    const weekday = document.getElementById('edit-sr-weekday').value;
                    params.append('weekday', weekday);
                } else if (freq === 'monthly') {
                    const monthday = document.getElementById('edit-sr-monthday').value;
                    params.append('monthday', monthday);
                }
                
                // Add the appropriate parameter based on report type
                if (type === 'services') {
                    params.append('hostname', entity);
                    // Add service description parameter
                    const serviceDescription = document.getElementById('edit-sr-service-description').value;
                    if (serviceDescription && serviceDescription !== 'all') {
                        params.append('serviceDescription', serviceDescription);
                    }
                } else {
                    params.append('hostgroup', entity);
                }
                
                // Append chosen status filters
                const hostStatuses = Array.from(document.querySelectorAll('#edit-sr-host-statuses input:checked')).map(cb=>cb.value).join(',');
                const svcStatuses = Array.from(document.querySelectorAll('#edit-sr-svc-statuses input:checked')).map(cb=>cb.value).join(',');
                params.append('hostStatuses', hostStatuses);
                params.append('svcStatuses', svcStatuses);
                params.append('saveToServer', document.getElementById('edit-sr-save-to-server').checked);

                const saveBtn = document.getElementById('edit-sr-save');
                saveBtn.disabled = true;
                saveBtn.textContent = 'Updating...';

                const success = await window.scheduledReportsUI.updateScheduledReport(params.toString());
                
                if (success) {
                    // Close the form and show the list
                    const reportsList = document.getElementById('scheduled-reports-list');
                    const editForm = document.getElementById('edit-report-form');
                    const addForm = document.getElementById('add-report-form');
                    const addBtn = document.getElementById('add-new-report-btn');
                    const searchBar = document.querySelector('.scheduled-reports-search');
                    const serviceDescriptionField = document.getElementById('edit-sr-service-description-field');
                    
                    if(reportsList && editForm && addForm && addBtn){
                        reportsList.style.display = 'block';
                        editForm.style.display = 'none';
                        addForm.style.display = 'none';
                        addBtn.style.display = 'inline-block';
                        if(searchBar) searchBar.style.display = 'block';
                        if(serviceDescriptionField) serviceDescriptionField.style.display = 'none';
                    }
                }
                
                saveBtn.disabled = false;
                saveBtn.textContent = 'Update Report';
            });
        }
        

        
        // Populate service descriptions for scheduled reports
        async function populateScheduledReportServiceDescriptions(selectElement, hostname = '') {
            if (!selectElement) return;
            
            // Clear existing options except the first one
            while (selectElement.options.length > 1) {
                selectElement.remove(1);
            }
            
            try {
                let url = '/nagios/cgi-bin/objectjson.cgi?query=servicelist';
                if (hostname && hostname !== 'all') {
                    url += '&hostname=' + encodeURIComponent(hostname);
                }
                
                const response = await fetch(url, { credentials: 'include' });
                if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
                
                const data = await response.json();
                const servicelist = data?.data?.servicelist || {};
                
                // Collect all unique service descriptions
                const serviceDescriptions = new Set();
                Object.values(servicelist).forEach(services => {
                    if (Array.isArray(services)) {
                        services.forEach(service => serviceDescriptions.add(service));
                    }
                });
                
                // Sort and add to dropdown
                const sortedServices = Array.from(serviceDescriptions).sort();
                sortedServices.forEach(service => {
                    const opt = document.createElement('option');
                    opt.value = service;
                    opt.textContent = service;
                    selectElement.appendChild(opt);
                });
            } catch (error) {
                console.error('Error fetching service descriptions for scheduled reports:', error);
            }
        }

            // Handle frequency change for scheduled reports
    function handleFrequencyChange(prefix) {
        const frequencySelect = document.getElementById(prefix + '-frequency');
        const rangeInput = document.getElementById(prefix + '-range');
        const weekdayField = document.getElementById(prefix + '-weekday-field');
        const monthdayField = document.getElementById(prefix + '-monthday-field');
        
        if (!frequencySelect || !rangeInput) return;
        
        const frequency = frequencySelect.value;
        
        // Update range based on frequency
        switch (frequency) {
            case 'daily':
                rangeInput.value = 1;
                if (weekdayField) weekdayField.style.display = 'none';
                if (monthdayField) monthdayField.style.display = 'none';
                break;
            case 'weekly':
                rangeInput.value = 7;
                if (weekdayField) weekdayField.style.display = 'flex';
                if (monthdayField) monthdayField.style.display = 'none';
                break;
            case 'monthly':
                rangeInput.value = 31;
                if (weekdayField) weekdayField.style.display = 'none';
                if (monthdayField) monthdayField.style.display = 'flex';
                break;
        }
    }

    // Populate dropdowns for scheduled reports
    function populateScheduledReportDropdowns() {
        const entitySelect = document.getElementById('sr-entity');
        const serviceDescriptionSelect = document.getElementById('sr-service-description');
        const reportType = document.getElementById('sr-type').value;
        
        if (entitySelect) {
            // Clear existing options except the first one
            while (entitySelect.options.length > 1) {
                entitySelect.remove(1);
            }
            
            const nowSec = Math.floor(Date.now() / 1000);
            const objectType = reportType === 'services' ? 'hosts' : 'hostgroups';
            const url = `/nagios/cgi-bin/archivejson.cgi?query=availability&availabilityobjecttype=${objectType}&starttime=${nowSec - 3600}&endtime=${nowSec}`;
            
            fetch(url, { credentials: 'include' })
                .then(r => (r.ok ? r.json() : Promise.reject(r.status)))
                .then(js => {
                    let arr = [];
                    if (objectType === 'hosts') {
                        arr = js?.data?.hosts?.map(h => h.name) || (js?.data?.host ? [js.data.host.name] : []);
                    } else {
                        arr = js?.data?.hostgroups?.map(g => g.name) || (js?.data?.hostgroup ? [js.data.hostgroup.name] : []);
                    }
                    
                    if (arr.length) {
                        arr.sort().forEach(v => {
                            const opt = document.createElement('option');
                            opt.value = v;
                            opt.textContent = v;
                            entitySelect.appendChild(opt);
                        });
                    }
                })
                .catch(() => {
                    console.warn(`Failed to populate ${objectType} dropdown`);
                });
        }
        
        // Populate service descriptions if it's a services report
        if (reportType === 'services' && serviceDescriptionSelect) {
            const entityValue = document.getElementById('sr-entity').value;
            populateScheduledReportServiceDescriptions(serviceDescriptionSelect, entityValue);
        }
    }

        // Handle report type change
        const srTypeSelect = document.getElementById('sr-type');
        if (srTypeSelect) {
            srTypeSelect.addEventListener('change', function() {
                // Update the label based on report type
                const entityLabel = document.querySelector('label[for="sr-entity"]');
                const serviceDescriptionField = document.getElementById('sr-service-description-field');
                if (entityLabel) {
                    entityLabel.textContent = this.value === 'services' ? 'Host (optional)' : 'Hostgroup (optional)';
                }
                
                // Show/hide service description field
                if (serviceDescriptionField) {
                    serviceDescriptionField.style.display = this.value === 'services' ? 'block' : 'none';
                }
                
                // Repopulate the dropdown with the appropriate data
                populateScheduledReportDropdowns();
            });
        }

        // Handle frequency change for new scheduled reports
        const srFrequencySelect = document.getElementById('sr-frequency');
        if (srFrequencySelect) {
            srFrequencySelect.addEventListener('change', function() {
                handleFrequencyChange('sr');
            });
        }

        // Handle frequency change for edit scheduled reports
        const editSrFrequencySelect = document.getElementById('edit-sr-frequency');
        if (editSrFrequencySelect) {
            editSrFrequencySelect.addEventListener('change', function() {
                handleFrequencyChange('edit-sr');
            });
        }

        // Handle entity change for service descriptions
        const srEntitySelect = document.getElementById('sr-entity');
        if (srEntitySelect) {
            srEntitySelect.addEventListener('change', function() {
                const reportType = document.getElementById('sr-type').value;
                if (reportType === 'services') {
                    const serviceDescriptionSelect = document.getElementById('sr-service-description');
                    if (serviceDescriptionSelect) {
                        populateScheduledReportServiceDescriptions(serviceDescriptionSelect, this.value);
                    }
                }
            });
        }

        if(srForm){
            srForm.addEventListener('submit', async function(e){
                e.preventDefault();
                const name = document.getElementById('sr-name').value.trim();
                const email = document.getElementById('sr-email').value.trim();
                const freq = document.getElementById('sr-frequency').value;
                const time = document.getElementById('sr-time').value || '00:05';
                const range = parseInt(document.getElementById('sr-range').value, 10) || 1;
                const type = document.getElementById('sr-type').value;
                const entity = document.getElementById('sr-entity').value;

                // Validate required fields
                if (!name || !email) {
                    alert('Please provide a report name and email address.');
                    return;
                }

                // Validate multiple emails separated by semicolons
                const emails = email.split(';').map(e => e.trim()).filter(e => e.length > 0);
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                for (const singleEmail of emails) {
                    if (!emailRegex.test(singleEmail)) {
                        alert('Please provide valid email address(es). Invalid email: ' + singleEmail);
                        return;
                    }
                }
                if (emails.length === 0) {
                    alert('Please provide at least one valid email address.');
                    return;
                }

                const params = new URLSearchParams();
                params.append('action', 'add');
                params.append('name', name);
                params.append('email', email);
                params.append('frequency', freq);
                params.append('time', time);
                params.append('range', range);
                params.append('type', type);
                
                // Add day selection parameters
                if (freq === 'weekly') {
                    const weekday = document.getElementById('sr-weekday').value;
                    params.append('weekday', weekday);
                } else if (freq === 'monthly') {
                    const monthday = document.getElementById('sr-monthday').value;
                    params.append('monthday', monthday);
                }
                
                // Add the appropriate parameter based on report type
                if (type === 'services') {
                    params.append('hostname', entity);
                    // Add service description parameter
                    const serviceDescription = document.getElementById('sr-service-description').value;
                    if (serviceDescription && serviceDescription !== 'all') {
                        params.append('serviceDescription', serviceDescription);
                    }
                } else {
                    params.append('hostgroup', entity);
                }
                
                // Append chosen status filters
                const hostStatuses = Array.from(document.querySelectorAll('#sr-host-statuses input:checked')).map(cb=>cb.value).join(',');
                const svcStatuses = Array.from(document.querySelectorAll('#sr-svc-statuses input:checked')).map(cb=>cb.value).join(',');
                params.append('hostStatuses', hostStatuses);
                params.append('svcStatuses', svcStatuses);
                params.append('saveToServer', document.getElementById('sr-save-to-server').checked);

                const saveBtn = document.getElementById('sr-save');
                saveBtn.disabled = true;
                saveBtn.textContent = 'Adding...';

                const success = await window.scheduledReportsUI.addScheduledReport(params.toString());
                
                if (success) {
                    // Close the form and show the list
                    const reportsList = document.getElementById('scheduled-reports-list');
                    const form = document.getElementById('add-report-form');
                    const addBtn = document.getElementById('add-new-report-btn');
                    const searchBar = document.querySelector('.scheduled-reports-search');
                    const serviceDescriptionField = document.getElementById('sr-service-description-field');
                    
                    if(reportsList && form && addBtn){
                        reportsList.style.display = 'block';
                        form.style.display = 'none';
                        addBtn.style.display = 'inline-block';
                        if(searchBar) searchBar.style.display = 'block';
                        if(serviceDescriptionField) serviceDescriptionField.style.display = 'none';
                    }
                }
                
                saveBtn.disabled = false;
                saveBtn.textContent = 'Add Report';
            });
        }
        

    }

    // Wait for all necessary elements and modules to be ready
    function waitForReady() {
        return new Promise((resolve) => {
            const checkReady = () => {
                // Check if all required elements exist
                const requiredElements = [
                    'report-start',
                    'report-end', 
                    'report-type',
                    'report-hostgroup',
                    'reports-content'
                ];
                
                const allElementsExist = requiredElements.every(id => 
                    document.getElementById(id) !== null
                );
                
                // Check if date inputs have valid values
                const startInput = document.getElementById('report-start');
                const endInput = document.getElementById('report-end');
                const datesValid = startInput && endInput && 
                                 startInput.value && endInput.value &&
                                 startInput.value.trim() !== '' && endInput.value.trim() !== '';
                
                // Check if required modules are loaded
                const modulesReady = window.reportsCore && 
                                   window.reportsCore.generateReport &&
                                   window.reportsRenderer &&
                                   window.reportsFilters;
                
                if (allElementsExist && datesValid && modulesReady) {
                    resolve();
                } else {
                    // Check again in a short interval
                    setTimeout(checkReady, 50);
                }
            };
            
            // Start checking
            checkReady();
        });
    }

    // Initialize UI
    async function init() {
        initializeElements();
        await initDates(); // Wait for dates to be initialized
        buildStatusFilters(typeSel ? typeSel.value : 'hostgroups');
        setupEventListeners();
        
        // Wait for everything to be ready before generating initial report
        await waitForReady();
        
        // Generate initial report automatically
        if (window.reportsCore && window.reportsCore.generateReport) {
            window.reportsCore.generateReport();
        }
    }

    // Export to global scope
    window.reportsUI = {
        init,
        getSelectedType,
        getStartInput,
        getEndInput,
        getHostgroupValue,
        getServiceDescriptionValue,
        getActiveStatuses,
        getSearchTerm,
        showLoading,
        showError,
        showEmpty,
        buildStatusFilters
    };
})(); 