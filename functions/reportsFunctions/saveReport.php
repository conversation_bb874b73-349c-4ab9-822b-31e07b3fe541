<?php
// saveReport.php - handles saving reports to the server for later access
// Expected POST params: type, startTs, endTs, hostStatuses, svcStatuses, hostgroup, email (optional)

header('Content-Type: application/json');
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Load environment variables (DB/Nagios credentials)
require_once dirname(__DIR__, 2) . '/loadenv.php';

// ------------------------- Helper functions (shared with sendScheduledReport.php) -------------------------------
/**
 * Get DB connection to 'blesk' for retrieving user credentials
 */
function getDatabaseConnectionAdminUser(): mysqli {
    $conn = new mysqli($_ENV['DB_SERVER'], $_ENV['DB_USER'], $_ENV['DB_PASSWORD'], 'blesk');
    if ($conn->connect_error) {
        die('DB connection failed: ' . $conn->connect_error);
    }
    return $conn;
}

/**
 * Fetch Nagios HTTP basic auth credentials (user_id=1)
 */
function getUserCredentials(): ?array {
    $conn = getDatabaseConnectionAdminUser();
    $result = $conn->query("SELECT username, password FROM users WHERE user_id = 1 LIMIT 1");
    $cred = null;
    if ($result && $row = $result->fetch_assoc()) {
        $cred = ['user' => $row['username'], 'pass' => $row['password']];
    }
    $conn->close();
    return $cred;
}

/**
 * Determine self IP address (stored by bubblemaps)
 */
function getSelfIp(): string {
    $ip = trim(@file_get_contents('/etc/sysconfig/ipaddr'));
    if (!$ip) die("Unable to determine self IP");
    return $ip;
}

// -----------------------------------------------------------------------------
// Configuration / constants
// -----------------------------------------------------------------------------
$FPDF_PATH     = __DIR__ . '/fpdf.php'; // optional FPDF library path
$NAGIOS_BASE   = 'https://' . getSelfIp(); // Use real host/IP instead of localhost
$REPORTS_DIR   = __DIR__ . '/saved_reports'; // Directory to store saved reports
$HOSTGROUP_URL = '/nagios/cgi-bin/archivejson.cgi?query=availability&availabilityobjecttype=hostgroups';
$SERVICE_URL   = '/nagios/cgi-bin/archivejson.cgi?query=availability&availabilityobjecttype=services';

// Create reports directory if it doesn't exist
if (!is_dir($REPORTS_DIR)) {
    if (!mkdir($REPORTS_DIR, 0755, true)) {
        echo json_encode(['success' => false, 'message' => 'Failed to create reports directory']);
        exit;
    }
}

// -----------------------------------------------------------------------------
// Get POST parameters
// -----------------------------------------------------------------------------
$type = isset($_POST['type']) ? trim($_POST['type']) : 'hostgroups';
$startTs = isset($_POST['startTs']) ? intval($_POST['startTs']) : 0;
$endTs = isset($_POST['endTs']) ? intval($_POST['endTs']) : 0;
$hostStatusesStr = strtolower(trim($_POST['hostStatuses'] ?? 'up,down,unreachable', "'\""));
$svcStatusesStr  = strtolower(trim($_POST['svcStatuses'] ?? 'ok,warning,critical,unknown', "'\""));
$hostStatuses = array_filter(explode(',', $hostStatusesStr));
$svcStatuses  = array_filter(explode(',', $svcStatusesStr));
$hostgroup = isset($_POST['hostgroup']) ? trim($_POST['hostgroup']) : '';
$serviceDescription = isset($_POST['serviceDescription']) ? trim($_POST['serviceDescription']) : '';
$email = isset($_POST['email']) ? trim($_POST['email']) : '';
$customName = isset($_POST['customName']) ? trim($_POST['customName']) : '';

// Validate parameters
if (!in_array($type, ['hostgroups', 'services'])) {
    echo json_encode(['success' => false, 'message' => 'Invalid report type']);
    exit;
}

if ($startTs <= 0 || $endTs <= 0 || $endTs <= $startTs) {
    echo json_encode(['success' => false, 'message' => 'Invalid time range']);
    exit;
}

// -----------------------------------------------------------------------------
// Helper to fetch hostname -> IP address map via Nagios objectjson API
// -----------------------------------------------------------------------------
function fetchHostIpMap(): array {
    try {
        $url = $GLOBALS['NAGIOS_BASE'] . '/nagios/cgi-bin/objectjson.cgi?query=hostlist&details=true';
        
        static $creds = null;
        if ($creds === null) $creds = getUserCredentials();

        // Initialise cURL
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($ch, CURLOPT_TIMEOUT, 20);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

        // Pass basic-auth credentials if available
        if ($creds) {
            curl_setopt($ch, CURLOPT_USERPWD, $creds['user'] . ':' . $creds['pass']);
            curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        }

        $raw = curl_exec($ch);
        if ($raw === false) {
            error_log('[saveReport] cURL error fetching host IP map: ' . curl_error($ch));
            curl_close($ch);
            return [];
        }

        $http = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        if ($http !== 200) {
            error_log('[saveReport] HTTP ' . $http . ' fetching host IP map');
            return [];
        }

        $json = json_decode($raw, true);
        if (!is_array($json)) return [];
        
        $map = [];
        $hostlist = $json['data']['hostlist'] ?? [];
        foreach ($hostlist as $hostname => $obj) {
            if ($obj && isset($obj['address'])) {
                $map[$hostname] = $obj['address'];
            }
        }
        return $map;
    } catch (Exception $e) {
        error_log('[saveReport] Failed to fetch host IP map: ' . $e->getMessage());
        return [];
    }
}

// -----------------------------------------------------------------------------
// Helper to fetch Nagios availability data (same as sendScheduledReport.php)
// -----------------------------------------------------------------------------
function fetchJson(string $path, int $startTs, int $endTs, string $hostgroup = '', string $hostname = '', string $serviceDescription = ''): array
{
    $url = $GLOBALS['NAGIOS_BASE'] . $path . '&starttime=' . $startTs . '&endtime=' . $endTs;
    
    // Add hostgroup or hostname filter if provided
    if (!empty($hostgroup)) {
        $url .= '&hostgroup=' . urlencode($hostgroup);
    }
    if (!empty($hostname)) {
        $url .= '&hostname=' . urlencode($hostname);
    }
    if (!empty($serviceDescription)) {
        $url .= '&servicedescription=' . str_replace(' ', '+', $serviceDescription);
    }

    static $creds = null;
    if ($creds === null) $creds = getUserCredentials();

    // Initialise cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
    curl_setopt($ch, CURLOPT_TIMEOUT, 20);
    // Allow self-signed certificates (internal Nagios)
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    // Pass basic-auth credentials if available
    if ($creds) {
        curl_setopt($ch, CURLOPT_USERPWD, $creds['user'] . ':' . $creds['pass']);
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
    }

    $raw = curl_exec($ch);
    if ($raw === false) {
        error_log('[saveReport] cURL error: ' . curl_error($ch));
        curl_close($ch);
        return [];
    }

    $http = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    if ($http !== 200) {
        error_log('[saveReport] HTTP ' . $http . ' fetching ' . $url);
        return [];
    }

    $json = json_decode($raw, true);
    if (!is_array($json)) return [];
    return $json['data'] ?? [];
}

// -----------------------------------------------------------------------------
// Generate PDF report (same logic as sendScheduledReport.php)
// -----------------------------------------------------------------------------
$timestamp = date('Ymd_His');
$filename = 'blesk_report_' . $type . '_' . $timestamp . '.pdf';
$filepath = $REPORTS_DIR . '/' . $filename;

// If custom name is provided, sanitize it and use it in metadata
$displayName = '';
if (!empty($customName)) {
    // Sanitize the custom name for safe display
    $displayName = preg_replace('/[^a-zA-Z0-9\s\-_\.]/', '', $customName);
    $displayName = trim($displayName);
    if (strlen($displayName) > 100) {
        $displayName = substr($displayName, 0, 100);
    }
}

$fpdfAvailable = false;
if (!class_exists('FPDF') && file_exists($FPDF_PATH)) {
    require_once $FPDF_PATH;
}
if (class_exists('FPDF')) {
    $fpdfAvailable = true;
}

// After FPDF is available, define subclass for header/footer
if ($fpdfAvailable && !class_exists('BleskPDF')) {
    class BleskPDF extends FPDF {
        public string $titleText = '';
        public string $rangeText = '';
        public string $statusText = '';
        public string $logoPath = '';
        function Header() {
            // Logo (optional)
            if ($this->logoPath && file_exists($this->logoPath)) {
                $this->Image($this->logoPath, 10, 6, 25);
            }
            // Title
            $this->SetFont('Arial', 'B', 16);
            $this->Cell(0, 8, $this->titleText, 0, 1, 'C');
            // Status filters (above range, bigger font)
            if ($this->statusText) {
                $this->SetFont('Arial', 'B', 11);
                $this->Cell(0, 7, $this->statusText, 0, 1, 'C');
            }
            // Date range
            $this->SetFont('Arial', '', 10);
            $this->Cell(0, 6, $this->rangeText, 0, 1, 'C');
            $this->Ln(5);
        }
        function Footer() {
            $this->SetY(-12);
            $this->SetFont('Arial', 'I', 8);
            $this->Cell(0, 10, 'Page ' . $this->PageNo() . '/{nb}', 0, 0, 'C');
        }

        // Public helper to get printable width between margins
        public function ContentWidth(): float {
            return $this->GetPageWidth() - $this->lMargin - $this->rMargin;
        }
    }
}

if ($fpdfAvailable) {
    $rangeDays = ceil(($endTs - $startTs) / 86400);
    $titleRangeTxt = $rangeDays === 1 ? 'Last 24 Hours' : ('Last ' . $rangeDays . ' Days');
    
    // Determine report type for title
    $reportType = '';
    if ($type === 'hostgroups') {
        $reportType = 'Host Availability Report';
    } elseif ($type === 'services') {
        $reportType = 'Service Availability Report';
    } else {
        $reportType = 'Availability Report';
    }
    
    $pdf = new BleskPDF('L', 'mm', 'A4');
    $pdf->AliasNbPages();
    $pdf->SetAuthor('Blesk');
    // Header content
    $pdf->titleText  = 'Blesk ' . $reportType . ' (' . $titleRangeTxt . ')';
    
    // Build status text based on report type
    $statusTextParts = [];
    if ($type === 'hostgroups') {
        $statusTextParts[] = 'Host statuses: ' . implode(', ', $hostStatuses);
    }
    if ($type === 'services') {
        $statusTextParts[] = 'Service statuses: ' . implode(', ', $svcStatuses);
    }
    $pdf->statusText = implode(' | ', $statusTextParts);
    $pdf->rangeText  = 'Range: ' . date('Y-m-d H:i', $startTs) . ' - ' . date('Y-m-d H:i', $endTs);
    // Use the new logo
    $logoPath = __DIR__ . '/blesk-logo-black_ret.png';
    if (file_exists($logoPath)) {
        $pdf->logoPath = $logoPath;
    }
    $pdf->SetTitle($pdf->titleText);
    $pdf->AddPage();

    // Fetch host IP map for displaying IP addresses
    $hostIpMap = fetchHostIpMap();

    // Common helpers ------------------------------------------------------
    $pdf->SetAutoPageBreak(true, 15);

    /**
     * Draw a light grey section title spanning full width with better styling
     */
    $drawSectionTitle = function(string $title) use ($pdf) {
        $pdf->SetFont('Arial', 'B', 14);
        $pdf->SetFillColor(240, 240, 240);
        $pdf->SetTextColor(51, 51, 51);
        $pdf->Cell(0, 10, utf8_decode($title), 0, 1, 'L', true);
        $pdf->Ln(3);
    };

    /**
     * Draw a table header row with uniform style
     */
    $drawTableHeader = function(array $headers, array $widths) use ($pdf) {
        $pdf->SetFont('Arial', 'B', 9);
        $pdf->SetFillColor(245, 245, 245);
        $pdf->SetTextColor(51, 51, 51);
        foreach ($headers as $idx => $hdr) {
            $pdf->Cell($widths[$idx], 7, $hdr, 1, 0, 'C', true);
        }
        $pdf->Ln();
    };

    /**
     * Draw a numeric percentage cell using fill colours similar to jsPDF
     */
    $pctCell = function(float $value, float $width, string $kind) use ($pdf) {
        $fill = false;
        if ($value > 0) {
            switch ($kind) {
                case 'ok':
                case 'up':
                    $pdf->SetFillColor(165, 214, 167); // green
                    break;
                case 'warn':
                case 'warning':
                    $pdf->SetFillColor(255, 224, 130); // yellow
                    break;
                case 'crit':
                case 'down':
                case 'critical':
                    $pdf->SetFillColor(229, 115, 115); // red
                    break;
                default: // unknown / unreachable
                    $pdf->SetFillColor(169, 182, 201); // grey
            }
            $fill = true;
            $pdf->SetTextColor(51, 51, 51);
        }
        $pdf->Cell($width, 6, formatPercentage($value), 1, 0, 'R', $fill);
        if ($fill) $pdf->SetTextColor(0); // reset
    };

    // --------------------------------------------------------------------
    // Summary Section
    // --------------------------------------------------------------------
    function calculateSummary($data, $type) {
        if ($type === 'services') {
            $services = $data['services'] ?? [];
            $service = $data['service'] ?? null;

            // Handle both array format (multiple services) and single object format (specific service)
            $servicesToProcess = [];
            if (!empty($services) && is_array($services)) {
                $servicesToProcess = $services;
            } elseif ($service) {
                $servicesToProcess = [$service];
            }

            if (empty($servicesToProcess)) return null;
            
            $totalServices = count($servicesToProcess);
            $servicesWithWarnings = 0;
            $servicesWithCritical = 0;
            $servicesWithUnknown = 0;

            foreach ($servicesToProcess as $svc) {
                // Include scheduled time values in calculations
                $warningTime = intval($svc['time_warning'] ?? 0) + intval($svc['scheduled_time_warning'] ?? 0);
                $criticalTime = intval($svc['time_critical'] ?? 0) + intval($svc['scheduled_time_critical'] ?? 0);
                $unknownTime = intval($svc['time_unknown'] ?? 0) + intval($svc['scheduled_time_unknown'] ?? 0);
                
                if ($warningTime > 0) $servicesWithWarnings++;
                if ($criticalTime > 0) $servicesWithCritical++;
                if ($unknownTime > 0) $servicesWithUnknown++;
            }
            
            return [
                'total' => $totalServices,
                'warning' => $servicesWithWarnings,
                'critical' => $servicesWithCritical,
                'unknown' => $servicesWithUnknown,
                'warningPct' => $totalServices > 0 ? ($servicesWithWarnings / $totalServices * 100) : 0,
                'criticalPct' => $totalServices > 0 ? ($servicesWithCritical / $totalServices * 100) : 0,
                'unknownPct' => $totalServices > 0 ? ($servicesWithUnknown / $totalServices * 100) : 0
            ];
        } else {
            $hostgroups = $data['hostgroups'] ?? ($data['hostgroup'] ? [$data['hostgroup']] : []);
            if (empty($hostgroups)) return null;
            
            $totalHosts = 0;
            $hostsWithDown = 0;
            $hostsWithUnreachable = 0;
            
            foreach ($hostgroups as $grp) {
                $hostList = $grp['hosts'] ?? [];
                $totalHosts += count($hostList);
                foreach ($hostList as $h) {
                    // Include scheduled time values in calculations
                    $downTime = intval($h['time_down'] ?? 0) + intval($h['scheduled_time_down'] ?? 0);
                    $unreachableTime = intval($h['time_unreachable'] ?? 0) + intval($h['scheduled_time_unreachable'] ?? 0);
                    $upTime = intval($h['time_up'] ?? 0) + intval($h['scheduled_time_up'] ?? 0);
                    
                    // Determine primary status (prioritize down > unreachable > up)
                    if ($downTime > 0) {
                        $hostsWithDown++;
                    } elseif ($unreachableTime > 0) {
                        $hostsWithUnreachable++;
                    }
                    // Hosts with only up time are not counted in either category
                }
            }
            
            return [
                'total' => $totalHosts,
                'down' => $hostsWithDown,
                'unreachable' => $hostsWithUnreachable,
                'downPct' => $totalHosts > 0 ? ($hostsWithDown / $totalHosts * 100) : 0,
                'unreachablePct' => $totalHosts > 0 ? ($hostsWithUnreachable / $totalHosts * 100) : 0
            ];
        }
    }

    // --------------------------------------------------------------------
    // Hosts Availability (Hostgroups) – mimic jsPDF consolidated layout
    // --------------------------------------------------------------------
    // Only pass hostgroup parameter if it's not empty and not 'all'
    $hostgroupParam = (!empty($hostgroup) && $hostgroup !== 'all') ? $hostgroup : '';
    $hostData = fetchJson($HOSTGROUP_URL, $startTs, $endTs, $hostgroupParam);
    $hostgroups = $hostData['hostgroups'] ?? ($hostData['hostgroup'] ? [$hostData['hostgroup']] : []);

    // Calculate and display summary
    $summary = null;
    if ($type === 'services') {
        // For services, if hostgroup is provided, it should be passed as hostname parameter
        $hostnameParam = (!empty($hostgroup) && $hostgroup !== 'all') ? $hostgroup : '';
        $svcData = fetchJson($SERVICE_URL, $startTs, $endTs, '', $hostnameParam, $serviceDescription);
        $summary = calculateSummary($svcData, $type);
    } else {
        $summary = calculateSummary($hostData, $type);
    }
    
    if ($summary) {
        $drawSectionTitle('SUMMARY - ' . ($type === 'services' ? 'Service' : 'Host') . ' Status Overview');
        
        $pdf->SetFont('Arial', 'B', 10);
        $pdf->SetFillColor(245, 245, 245);
        $pdf->SetTextColor(51, 51, 51);
        
        if ($type === 'services') {
            $pdf->Cell(60, 8, 'Total Monitored Services', 1, 0, 'L', true);
            $pdf->Cell(30, 8, $summary['total'], 1, 1, 'C');
            
            $pdf->SetFont('Arial', '', 9);
            $pdf->Cell(60, 6, 'Services with Warning State', 1, 0, 'L');
            $pdf->Cell(30, 6, $summary['warning'] . ' (' . formatPercentage($summary['warningPct']) . '%)', 1, 1, 'C');
            
            $pdf->Cell(60, 6, 'Services with Critical State', 1, 0, 'L');
            $pdf->Cell(30, 6, $summary['critical'] . ' (' . formatPercentage($summary['criticalPct']) . '%)', 1, 1, 'C');
            
            $pdf->Cell(60, 6, 'Services with Unknown State', 1, 0, 'L');
            $pdf->Cell(30, 6, $summary['unknown'] . ' (' . formatPercentage($summary['unknownPct']) . '%)', 1, 1, 'C');
        } else {
            $pdf->Cell(60, 8, 'Total Monitored Hosts', 1, 0, 'L', true);
            $pdf->Cell(30, 8, $summary['total'], 1, 1, 'C');
            
            $pdf->SetFont('Arial', '', 9);
            $pdf->Cell(60, 6, 'Hosts with Down State', 1, 0, 'L');
            $pdf->Cell(30, 6, $summary['down'] . ' (' . formatPercentage($summary['downPct']) . '%)', 1, 1, 'C');
            
            $pdf->Cell(60, 6, 'Hosts with Unreachable State', 1, 0, 'L');
            $pdf->Cell(30, 6, $summary['unreachable'] . ' (' . formatPercentage($summary['unreachablePct']) . '%)', 1, 1, 'C');
        }
        
        $pdf->Ln(5);
    }

    // Only show hosts section if we have host data and it's not a services-only report
    if (!empty($hostgroups) && $type !== 'services') {
        $drawSectionTitle('Host Availability Summary');
        
        foreach ($hostgroups as $grp) {
            $allHosts = $grp['hosts'] ?? [];
            $filteredHosts = array_filter($allHosts, function($h) use ($hostStatuses){
                // Determine all applicable statuses for filtering (same logic as HTML reports)
                $statuses = [];
                if (intval($h['time_down']) > 0) $statuses[] = 'down';
                if (intval($h['time_unreachable']) > 0) $statuses[] = 'unreachable';
                if (intval($h['time_up']) > 0) $statuses[] = 'up';
                
                // Show host if ANY of its statuses match the active filters
                foreach ($statuses as $status) {
                    if (in_array($status, $hostStatuses, true)) {
                        return true;
                    }
                }
                return false;
            });
            if(empty($filteredHosts)) continue;

            // Section (hostgroup) title with better styling
            $pdf->SetFont('Arial', 'B', 11);
            $pdf->SetFillColor(250, 250, 250);
            $pdf->SetTextColor(51, 51, 51);
            $pageWidth = $pdf->ContentWidth();
            $pdf->Cell($pageWidth, 8, utf8_decode($grp['name'] ?? 'Unnamed Hostgroup'), 0, 1, 'L', true);

            // Table headers
            $hdrs = ['Host', 'Up %', 'Down %', 'Unreach %'];
            $pageWidth = $pdf->ContentWidth();
            $pctW = 25; // fixed width for percentage columns
            $widths = [$pageWidth - 3 * $pctW, $pctW, $pctW, $pctW];
            $drawTableHeader($hdrs, $widths);

            // Rows
            $pdf->SetFont('Arial', '', 8);
            foreach ($filteredHosts as $h) {
                // Include scheduled time values in total calculation
                $total = (intval($h['time_up']) + intval($h['time_down']) + intval($h['time_unreachable']) +
                         intval($h['scheduled_time_up'] ?? 0) + intval($h['scheduled_time_down'] ?? 0) + intval($h['scheduled_time_unreachable'] ?? 0));
                $upPct  = percent(intval($h['time_up']) + intval($h['scheduled_time_up'] ?? 0), $total);
                $downPct = percent(intval($h['time_down']) + intval($h['scheduled_time_down'] ?? 0), $total);
                $unPct  = percent(intval($h['time_unreachable']) + intval($h['scheduled_time_unreachable'] ?? 0), $total);

                // Add IP address if available
                $hostDisplay = $h['name'] ?? '-';
                $ip = $hostIpMap[$h['name']] ?? null;
                if ($ip && $ip !== $h['name']) {
                    $hostDisplay = $h['name'] . ' (' . $ip . ')';
                }

                $pdf->Cell($widths[0], 6, $hostDisplay, 1);
                $pctCell($upPct,   $widths[1], 'up');
                $pctCell($downPct, $widths[2], 'down');
                $pctCell($unPct,   $widths[3], 'unknown');
                $pdf->Ln();
            }
            $pdf->Ln(3);
        }
    } elseif ($type === 'hostgroups') {
        $pdf->SetFont('Arial', '', 10);
        $pdf->Cell(0, 6, 'No host data available', 0, 1);
    }

    // --------------------------------------------------------------------
    // Services Availability – mimic jsPDF consolidated layout
    // --------------------------------------------------------------------
    $services = $svcData['services'] ?? [];
    $service = $svcData['service'] ?? null;

    // Handle both array format (multiple services) and single object format (specific service)
    $servicesToDisplay = [];
    if (!empty($services) && is_array($services)) {
        $servicesToDisplay = $services;
    } elseif ($service) {
        $servicesToDisplay = [$service];
    }

    // Only show services section if we have service data and it's not a hosts-only report
    if (!empty($servicesToDisplay) && $type !== 'hostgroups') {
        $drawSectionTitle('Service Availability Summary');
        
        // Group by host like in JS implementation, with status filtering
        $byHost = [];
        foreach ($servicesToDisplay as $svc) {
            // Determine all applicable statuses for filtering (same logic as HTML reports)
            $statuses = [];
            if (intval($svc['time_critical']) + intval($svc['scheduled_time_critical'] ?? 0) > 0) $statuses[] = 'critical';
            if (intval($svc['time_warning']) + intval($svc['scheduled_time_warning'] ?? 0) > 0) $statuses[] = 'warning';
            if (intval($svc['time_unknown']) + intval($svc['scheduled_time_unknown'] ?? 0) > 0) $statuses[] = 'unknown';
            if (intval($svc['time_ok']) + intval($svc['scheduled_time_ok'] ?? 0) > 0) $statuses[] = 'ok';
            
            // Show service if ANY of its statuses match the active filters
            $shouldShow = false;
            foreach ($statuses as $status) {
                if (in_array($status, $svcStatuses, true)) {
                    $shouldShow = true;
                    break;
                }
            }
            
            if (!$shouldShow) continue;
            $byHost[$svc['host_name']][] = $svc;
        }

        foreach ($byHost as $host => $svcList) {
            if(empty($svcList)) continue;
            // Host title row with better styling
            $pdf->SetFont('Arial', 'B', 11);
            $pdf->SetFillColor(250, 250, 250);
            $pdf->SetTextColor(51, 51, 51);
            $pageWidth = $pdf->ContentWidth();
            
            // Add IP address if available
            $hostDisplay = $host;
            $ip = $hostIpMap[$host] ?? null;
            if ($ip && $ip !== $host) {
                $hostDisplay = $host . ' (' . $ip . ')';
            }
            
            $pdf->Cell($pageWidth, 8, utf8_decode($hostDisplay), 0, 1, 'L', true);

            // Table headers
            $hdrsSvc = ['Service', 'OK %', 'Warn %', 'Crit %', 'Unk %'];
            $pageWidth = $pdf->ContentWidth();
            $pctW = 25;
            $wSvc  = [$pageWidth - 4 * $pctW, $pctW, $pctW, $pctW, $pctW];
            $drawTableHeader($hdrsSvc, $wSvc);

            $pdf->SetFont('Arial', '', 8);
            foreach ($svcList as $svc) {
                // Include scheduled time values in total calculation
                $total = (intval($svc['time_ok']) + intval($svc['time_warning']) + intval($svc['time_critical']) + intval($svc['time_unknown']) +
                         intval($svc['scheduled_time_ok'] ?? 0) + intval($svc['scheduled_time_warning'] ?? 0) + intval($svc['scheduled_time_critical'] ?? 0) + intval($svc['scheduled_time_unknown'] ?? 0));
                $okPct   = percent(intval($svc['time_ok']) + intval($svc['scheduled_time_ok'] ?? 0), $total);
                $warnPct = percent(intval($svc['time_warning']) + intval($svc['scheduled_time_warning'] ?? 0), $total);
                $critPct = percent(intval($svc['time_critical']) + intval($svc['scheduled_time_critical'] ?? 0), $total);
                $unkPct  = percent(intval($svc['time_unknown']) + intval($svc['scheduled_time_unknown'] ?? 0), $total);

                $pdf->Cell($wSvc[0], 6, $svc['description'] ?? '-', 1);
                $pctCell($okPct,   $wSvc[1], 'ok');
                $pctCell($warnPct, $wSvc[2], 'warn');
                $pctCell($critPct, $wSvc[3], 'crit');
                $pctCell($unkPct,  $wSvc[4], 'unknown');
                $pdf->Ln();
            }
            $pdf->Ln(3);
        }
    } elseif ($type === 'services') {
        $pdf->SetFont('Arial', '', 10);
        $pdf->Cell(0, 6, 'No service data available', 0, 1);
    }

    $pdf->Output('F', $filepath);
} else {
    // FPDF unavailable -> create very small placeholder PDF
    file_put_contents($filepath, "%PDF-1.4\n1 0 obj<<>>endobj\ntrailer<<>>\n%%EOF");
}

// -----------------------------------------------------------------------------
// Save report metadata
// -----------------------------------------------------------------------------
$metadata = [
    'filename' => $filename,
    'type' => $type,
    'startTs' => $startTs,
    'endTs' => $endTs,
    'hostStatuses' => $hostStatuses,
    'svcStatuses' => $svcStatuses,
    'hostgroup' => $hostgroup,
    'serviceDescription' => $serviceDescription,
    'email' => $email,
    'customName' => $displayName,
    'created' => time(),
    'createdDate' => date('Y-m-d H:i:s'),
    'rangeDays' => ceil(($endTs - $startTs) / 86400)
];

$metadataFile = $REPORTS_DIR . '/' . pathinfo($filename, PATHINFO_FILENAME) . '.json';
file_put_contents($metadataFile, json_encode($metadata, JSON_PRETTY_PRINT));

// -----------------------------------------------------------------------------
// Send email if requested
// -----------------------------------------------------------------------------
if (!empty($email)) {
    $SENDMAIL_PATH = '/usr/sbin/sendmail';
    $sender = trim(@file_get_contents('/var/lib/blesk/emailsender')) ?: 'blesk@localhost';
    
    // Validate multiple emails separated by semicolons
    $recipients = [];
    $emails = array_map('trim', explode(';', $email));
    foreach ($emails as $singleEmail) {
        if (!empty($singleEmail) && filter_var($singleEmail, FILTER_VALIDATE_EMAIL)) {
            $recipients[] = $singleEmail;
        }
    }
    
    if (!empty($recipients)) {
        $subject = 'Blesk Report (Last ' . $metadata['rangeDays'] . ($metadata['rangeDays']===1?' Day':' Days').') - ' . date('Y-m-d');
        
        // Email MIME building with plain+HTML and attachment
        $boundary  = '==Mixed_' . md5(time()) . 'x';
        $altBoundary = '==Alt_' . md5(time() + 1) . 'x';

        $headers   = 'From: ' . $sender . "\r\n";
        $headers  .= 'MIME-Version: 1.0' . "\r\n";
        $headers  .= 'Content-Type: multipart/mixed; boundary="' . $boundary . '"' . "\r\n";

        $htmlBody = '<p>Please find attached the Blesk availability report for the last ' . $metadata['rangeDays'] . ($metadata['rangeDays']===1?' day':' days') . '.</p>';

        $body  = '--' . $boundary . "\r\n";
        $body .= 'Content-Type: multipart/alternative; boundary="' . $altBoundary . '"' . "\r\n\r\n";

        // Plain text part
        $body .= '--' . $altBoundary . "\r\n";
        $body .= 'Content-Type: text/plain; charset="UTF-8"' . "\r\n";
        $body .= 'Content-Transfer-Encoding: 7bit' . "\r\n\r\n";
        $body .= "Please find attached the Blesk availability report for the last " . $metadata['rangeDays'] . ($metadata['rangeDays']===1?' day':' days') . ".\r\n\r\n";

        // HTML part
        $body .= '--' . $altBoundary . "\r\n";
        $body .= 'Content-Type: text/html; charset="UTF-8"' . "\r\n";
        $body .= 'Content-Transfer-Encoding: 7bit' . "\r\n\r\n";
        $body .= $htmlBody . "\r\n\r\n";

        $body .= '--' . $altBoundary . '--' . "\r\n";

        // Attach PDF
        $fileContent = chunk_split(base64_encode(file_get_contents($filepath)));
        $body .= '--' . $boundary . "\r\n";
        $body .= 'Content-Type: application/pdf; name="blesk_report.pdf"' . "\r\n";
        $body .= 'Content-Disposition: attachment; filename="blesk_report.pdf"' . "\r\n";
        $body .= 'Content-Transfer-Encoding: base64' . "\r\n\r\n";
        $body .= $fileContent . "\r\n";
        $body .= '--' . $boundary . '--';

        // Send using sendmail
        $cmd = escapeshellcmd($SENDMAIL_PATH) . ' -t -f ' . escapeshellarg($sender);
        $proc = popen($cmd, 'w');
        if ($proc !== false) {
            fwrite($proc, 'To: ' . implode(', ', $recipients) . "\n");
            fwrite($proc, 'Subject: ' . $subject . "\n");
            foreach (explode("\r\n", trim($headers)) as $hdrLine) {
                fwrite($proc, $hdrLine . "\n");
            }
            fwrite($proc, "\n" . $body);
            pclose($proc);
        }
    }
}

echo json_encode([
    'success' => true, 
    'message' => 'Report saved successfully' . (!empty($recipients) ? ' and sent via email' : ''),
    'filename' => $filename,
    'metadata' => $metadata
]);

// ------------------------ Helper ----------------------------
function percent($part, $total)
{
    if ($total == 0) return 0.0;
    return ($part / $total) * 100.0;
}

/**
 * Format percentage for display (show < 0.1 for very small values)
 */
function formatPercentage($value) {
    if ($value < 0.1 && $value > 0) {
        return '< 0.1';
    }
    return number_format($value, 1);
}
?> 