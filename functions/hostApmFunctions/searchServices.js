/**
 * Service Search Functionality
 * Handles filtering of service cards based on user input and status
 */

document.addEventListener('DOMContentLoaded', () => {
    // Get the search elements
    const searchInput = document.getElementById('service-search');
    const clearSearchBtn = document.getElementById('clear-search');
    const searchContainer = document.querySelector('.search-container');
    let filterButtons = [];
    let activeStatusFilter = 'all';
    
    // Initialize search only after services or SPM data are loaded
    const initSearchFunctionality = () => {
        // Check if services exist
        const serviceCards = document.querySelectorAll('.service-card');
        // Check if SPM tables exist
        const spmTables = document.querySelectorAll('.spm-table');
        
        // If no services AND no SPM tables, hide search container and retry later
        if (serviceCards.length === 0 && spmTables.length === 0) {
            searchContainer.classList.remove('visible');
            setTimeout(initSearchFunctionality, 500);
            return;
        }
        
        // Services or SPM tables exist, show search container
        searchContainer.classList.add('visible');
        
        // Show/hide service-specific controls based on whether services exist
        updateServiceControlsVisibility(serviceCards.length > 0);
        
        // Add event listeners for search
        searchInput.addEventListener('input', applyAllFilters);
        clearSearchBtn.addEventListener('click', clearSearch);
        
        // Add event listeners for status filter buttons
        filterButtons = document.querySelectorAll('.filter-btn');
        filterButtons.forEach(button => {
            // Store original label in dataset for future use
            if (!button.dataset.label) {
                button.dataset.label = button.textContent.trim();
            }
            button.addEventListener('click', () => {
                const status = button.getAttribute('data-status');
                setActiveStatusFilter(status);
                applyAllFilters();
            });
        });
        
        // Initialize with "All" as active
        setActiveStatusFilter('all');
        
        // Show/hide clear button based on search input
        searchInput.addEventListener('input', toggleClearButton);
        
        // ----- NEW: compute initial counts and visibility ----- //
        applyAllFilters();
        // ----------------------------------------------------- //
    };
    
    // Initialize after a delay to ensure services are loaded
    setTimeout(initSearchFunctionality, 500);
    
    // Also watch for DOM changes to handle dynamic service loading
    watchForServiceChanges();
    
    /**
     * Watch for changes in the service container to handle dynamically loaded services
     */
    function watchForServiceChanges() {
        const statusContainer = document.getElementById('status');
        if (!statusContainer) return;
        
        // Create a MutationObserver to watch for changes
        const observer = new MutationObserver(mutations => {
            for (const mutation of mutations) {
                if (mutation.type === 'childList') {
                    // Check if services exist after DOM changes
                    const serviceCards = document.querySelectorAll('.service-card');
                    if (serviceCards.length > 0) {
                        // Services exist, show search container if not already visible
                        if (!searchContainer.classList.contains('visible')) {
                            searchContainer.classList.add('visible');
                            // Only re-initialize if not already initialized
                            if (!searchInput.dataset.initialized) {
                                setTimeout(initSearchFunctionality, 100);
                            }
                        }
                    } else {
                        // No services, but don't hide search container if SPM tables exist
                        const spmTables = document.querySelectorAll('.spm-table');
                        if (spmTables.length === 0) {
                            searchContainer.classList.remove('visible');
                        }
                    }
                }
            }
        });
        
        // Start observing with configuration
        observer.observe(statusContainer, { childList: true, subtree: true });
    }
    
    /**
     * Set active status filter and update button UI
     * @param {string} status - The status to set active
     */
    function setActiveStatusFilter(status) {
        activeStatusFilter = status;
        
        // Update button UI
        filterButtons.forEach(button => {
            if (button.getAttribute('data-status') === status) {
                button.classList.add('active');
            } else {
                button.classList.remove('active');
            }
        });
    }
    
    /**
     * Apply both text search and status filters
     */
    function applyAllFilters() {
        const searchTerm = searchInput.value.trim().toLowerCase();
        const serviceCards = document.querySelectorAll('.service-card');
        const listRows = document.querySelectorAll('.services-table tbody tr');
        const spmRows = document.querySelectorAll('.spm-table tbody tr');
        let visibleCount = 0;
        
        // ----- NEW: prepare count map for statuses among cards that match current search term ----- //
        const statusCounters = {
            all: 0,
            ok: 0,
            warning: 0,
            critical: 0,
            unknown: 0,
            pending: 0
        };
        // ------------------------------------------------------------------------------------------ //
        
        serviceCards.forEach((card, idx) => {
            // Apply both filters - first check search text
            const isMatchingSearch = matchesSearchTerm(card, searchTerm);
            
            // Increment counters for buttons if card matches search term (regardless of status filter)
            if (isMatchingSearch) {
                statusCounters.all++;
                ['ok','warning','critical','unknown','pending'].forEach(stat => {
                    if (card.classList.contains(stat)) {
                        statusCounters[stat]++;
                    }
                });
            }
            
            // Then check status
            const isMatchingStatus = activeStatusFilter === 'all' || matchesStatusFilter(card, activeStatusFilter);
            
            // Show card only if it passes both filters
            if (isMatchingSearch && isMatchingStatus) {
                card.classList.remove('filtered');
                visibleCount++;
            } else {
                card.classList.add('filtered');
            }
        });
        
        // Apply same visibility to list rows if present
        if (listRows.length) {
            listRows.forEach(row => {
                // Identify service name cell text for search matching
                const nameCell = row.querySelector('.svc-name-cell');
                const statusCell = row.querySelector('.status-cell');
                const titleText = nameCell ? nameCell.textContent.toLowerCase() : '';
                const statusText = statusCell ? statusCell.textContent.toLowerCase() : '';

                const isMatchingSearch = searchTerm === '' || titleText.includes(searchTerm) || statusText.includes(searchTerm);

                const statusClassList = ['ok','warning','critical','unknown','pending'];
                let rowStatusClass = statusClassList.find(cls => row.classList.contains(`service-row-${cls}`));
                if (!rowStatusClass) rowStatusClass = statusClassList.find(cls => row.classList.contains(cls));

                const isMatchingStatus = activeStatusFilter === 'all' || rowStatusClass === activeStatusFilter;

                if (isMatchingSearch && isMatchingStatus) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }
        
        // Apply SPM table filtering
        applySpmFiltering(searchTerm);
        
        // Update select all checkbox state after filtering
        if (typeof updateSelectAllCheckbox === 'function') {
            setTimeout(() => updateSelectAllCheckbox(), 50);
        }
        
        // Update select all button text after filtering
        if (typeof updateSelectAllButtonText === 'function') {
            setTimeout(() => updateSelectAllButtonText(), 50);
        }
        
        // ----- NEW: Update button labels or hide if zero ----- //
        let activeStillVisible = true;
        filterButtons.forEach(button => {
            const statusKey = button.getAttribute('data-status');
            // 'all' filter should always remain visible
            if (statusKey !== 'all') {
                const count = statusCounters[statusKey];
                if (count === 0) {
                    button.style.display = 'none';
                    if (activeStatusFilter === statusKey) {
                        activeStillVisible = false;
                    }
                } else {
                    button.style.display = '';
                }
            }
            // Update label and badge for visible buttons (including 'all')
            if (button.style.display !== 'none') {
                const label = button.dataset.label || statusKey;
                const count = statusCounters[statusKey];
                if (statusKey === 'all') {
                    // For "All" filter, just display the label without the count badge
                    button.innerHTML = label;
                } else {
                    button.innerHTML = `${label}<span class="count-badge">${count}</span>`;
                }
            }
        });
        // If the previously active filter disappeared, reset to 'all'
        if (!activeStillVisible) {
            setActiveStatusFilter('all');
        }
        // -------------------------------------------------- //
        
        // Update service count display
        updateServiceCount(visibleCount, serviceCards.length);
    }
    
    // Make functions accessible to other modules (e.g., viewToggle.js, spmHandler.js)
    window.applyAllFilters = applyAllFilters;
    window.initSearchFunctionality = initSearchFunctionality;
    
    /**
     * Check if a card matches the search term
     * @param {Element} card - The service card element
     * @param {string} searchTerm - The search term to match
     * @returns {boolean} - Whether the card matches the search term
     */
    function matchesSearchTerm(card, searchTerm) {
        if (!searchTerm) return true; // Empty search matches everything
        
        const serviceTitle = card.querySelector('.service-title');
        if (!serviceTitle) return false;
        
        const titleText = serviceTitle.textContent.toLowerCase();
        const statusInfo = card.querySelector('.service-details');
        const statusText = statusInfo ? statusInfo.textContent.toLowerCase() : '';
        
        // Check if card contains the search term in title or status
        return titleText.includes(searchTerm) || statusText.includes(searchTerm);
    }
    
    /**
     * Check if a card matches the active status filter
     * @param {Element} card - The service card element
     * @param {string} statusFilter - The status to filter by
     * @returns {boolean} - Whether the card matches the status filter
     */
    function matchesStatusFilter(card, statusFilter) {
        if (statusFilter === 'all') return true;
        
        // Check card classes to determine status
        return card.classList.contains(statusFilter);
    }
    
    /**
     * Clear the search input and reset to show all service cards
     * but maintain status filter
     */
    function clearSearch() {
        searchInput.value = '';
        
        // Apply only status filter since search is cleared
        applyAllFilters();
        
        // Hide clear button
        clearSearchBtn.classList.remove('visible');
        
        // Focus on search input
        searchInput.focus();
    }
    
    /**
     * Toggle visibility of clear button based on search input
     */
    function toggleClearButton() {
        if (searchInput.value.trim() !== '') {
            clearSearchBtn.classList.add('visible');
        } else {
            clearSearchBtn.classList.remove('visible');
        }
    }
    
    /**
     * Update service count display
     * @param {number} visibleCount - Number of visible services
     * @param {number} totalCount - Total number of services
     */
    function updateServiceCount(visibleCount, totalCount) {
        const serviceCountElement = document.getElementById('service-count');
        if (serviceCountElement) {
            if (visibleCount < totalCount) {
                serviceCountElement.innerHTML = `<i class="fa fa-server" aria-hidden="true"></i> ${visibleCount} / ${totalCount} Services`;
            } else {
                serviceCountElement.innerHTML = `<i class="fa fa-server" aria-hidden="true"></i> ${totalCount} Services`;
            }
        }
    }
    
    /**
     * Apply search filtering to SPM table rows
     * @param {string} searchTerm - The search term to filter by
     */
    function applySpmFiltering(searchTerm) {
        const spmRows = document.querySelectorAll('.spm-table tbody tr');
        
        if (spmRows.length === 0) return; // No SPM table present
        
        spmRows.forEach(row => {
            // Skip filtering for rows in the "Switch port interface(s) this host is connected to" section
            // This section is in the spm-marketing-container
            const marketingContainer = document.getElementById('spm-marketing-container');
            if (marketingContainer && marketingContainer.contains(row)) {
                // Always show rows in the marketing container (switch port interfaces section)
                row.style.display = '';
                return;
            }
            
            if (!searchTerm) {
                // No search term, show all rows
                row.style.display = '';
                return;
            }
            
            // Get all text content from the row
            const rowText = row.textContent.toLowerCase();
            
            // Check if any cell content matches the search term
            if (rowText.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
        
        // Update SPM section visibility based on whether any rows are visible
        updateSpmSectionVisibility();
    }

    // Make applySpmFiltering globally accessible
    window.applySpmFiltering = applySpmFiltering;
    
    /**
     * Update service count display
     * @param {boolean} hasServices - Whether services are present
     */
    function updateServiceControlsVisibility(hasServices) {
        const viewToggleButton = document.getElementById('view-toggle-button');
        const selectionModeButton = document.getElementById('selection-mode-button');
        const servicesContainer = document.getElementById('status');
        
        if (viewToggleButton) {
            viewToggleButton.style.display = hasServices ? '' : 'none';
        }
        
        if (selectionModeButton) {
            selectionModeButton.style.display = hasServices ? '' : 'none';
        }
        
        // Hide/show the entire services container
        if (servicesContainer) {
            servicesContainer.style.display = hasServices ? '' : 'none';
        }
        
        // Also hide/show filter buttons and related elements (status filters) as they're service-specific
        const filterButtons = document.querySelectorAll('.filter-btn');
        const filterLabel = document.querySelector('.filter-label');
        const filterButtonsContainer = document.querySelector('.filter-buttons');
        
        filterButtons.forEach(button => {
            button.style.display = hasServices ? '' : 'none';
        });
        
        if (filterLabel) {
            filterLabel.style.display = hasServices ? '' : 'none';
        }
        
        if (filterButtonsContainer) {
            filterButtonsContainer.style.display = hasServices ? '' : 'none';
        }
        
        // Update the service count display appropriately
        const serviceCountElement = document.getElementById('service-count');
        if (serviceCountElement) {
            if (hasServices) {
                // Will be updated by updateServiceCount function
                serviceCountElement.style.display = '';
            } else {
                // Hide service count when no services
                serviceCountElement.style.display = 'none';
            }
        }
        
        // Update search placeholder text based on available content
        updateSearchPlaceholder(hasServices);
    }
    
    /**
     * Update search placeholder text based on available content
     * @param {boolean} hasServices - Whether services are present
     */
    function updateSearchPlaceholder(hasServices) {
        const searchInput = document.getElementById('service-search');
        if (!searchInput) return;
        
        searchInput.placeholder = 'Search services and connectivity...';
        searchInput.setAttribute('aria-label', 'Search services and connectivity');
    }
    
    /**
     * Update SPM section visibility based on filtered results
     */
    function updateSpmSectionVisibility() {
        const spmSections = document.querySelectorAll('.spm-section');
        
        spmSections.forEach(section => {
            // Skip visibility updates for sections in the marketing container (switch port interfaces)
            const marketingContainer = document.getElementById('spm-marketing-container');
            if (marketingContainer && marketingContainer.contains(section)) {
                // Always keep the switch port interfaces section visible
                const tableContainer = section.querySelector('.spm-table-container');
                if (tableContainer) {
                    tableContainer.style.display = '';
                }
                
                // Hide any "no results" message in this section
                const noResultsMsg = section.querySelector('.spm-no-results');
                if (noResultsMsg) {
                    noResultsMsg.style.display = 'none';
                }
                return;
            }
            
            const table = section.querySelector('.spm-table');
            if (!table) return;
            
            const visibleRows = table.querySelectorAll('tbody tr:not([style*="display: none"])');
            const tableContainer = section.querySelector('.spm-table-container');
            
            if (visibleRows.length === 0) {
                // Hide the table but keep the section header visible
                if (tableContainer) {
                    tableContainer.style.display = 'none';
                }
                
                // Show a "no results" message if it doesn't exist
                let noResultsMsg = section.querySelector('.spm-no-results');
                if (!noResultsMsg) {
                    noResultsMsg = document.createElement('div');
                    noResultsMsg.className = 'spm-no-results';
                    noResultsMsg.innerHTML = '<i class="fa fa-search"></i><p>No matching connectivity information found.</p>';
                    noResultsMsg.style.cssText = `
                        text-align: center;
                        padding: 20px;
                        color: var(--text-secondary);
                        font-style: italic;
                    `;
                    section.appendChild(noResultsMsg);
                }
                noResultsMsg.style.display = 'block';
            } else {
                // Show the table
                if (tableContainer) {
                    tableContainer.style.display = '';
                }
                
                // Hide the "no results" message
                const noResultsMsg = section.querySelector('.spm-no-results');
                if (noResultsMsg) {
                    noResultsMsg.style.display = 'none';
                }
            }
        });
    }
}); 