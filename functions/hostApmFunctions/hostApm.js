// Global variables
const urlParams = new URLSearchParams(window.location.search);
let realHostName = '';
let hostActionUrl = ''; // Store host action URL globally

// Function to blacklist a host in the APM context
function blacklistHostApm(hostname) {
    // Close any open context menus
    const contextMenu = document.getElementById('custom-context-menu');
    if (contextMenu) {
        contextMenu.style.display = 'none';
    }
    
    // Close any open modals
    const modal = document.getElementById('service-modal');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = "auto";
    }

    // Show a confirmation dialog to the user
    const isConfirmed = confirm("Are you sure you want to stop monitoring this host?");

    if (!isConfirmed) {
        return; // Exit the function if the user cancels
    }

    // Get the IP address from the URL parameters
    const ip = urlParams.get('hostip') || urlParams.get('ip');
    const infra = urlParams.get('infra');

    if (!ip) {
        alert("Error: Could not determine host IP address.");
        return;
    }

    // Show loading message
    const loadingDiv = document.createElement('div');
    loadingDiv.id = 'blacklist-loading';
    loadingDiv.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 20px;
        border-radius: 8px;
        z-index: 10000;
        text-align: center;
    `;
    loadingDiv.innerHTML = `
        <div style="margin-bottom: 10px;">
            <i class="fa fa-spinner fa-spin" style="font-size: 24px;"></i>
        </div>
        <div>Blacklisting host...</div>
    `;
    document.body.appendChild(loadingDiv);

    // Send the request to blacklist the host
    const xhr = new XMLHttpRequest();
    const blacklistUrl = `blacklistHost.php?ip=${encodeURIComponent(ip)}${infra ? '&infra=' + encodeURIComponent(infra) : ''}`;
    
    xhr.open('GET', blacklistUrl, true);
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            // Remove loading message
            const loadingElement = document.getElementById('blacklist-loading');
            if (loadingElement) {
                loadingElement.remove();
            }
            
            if (xhr.status === 200) {
                // Show success message briefly then redirect
                const successDiv = document.createElement('div');
                successDiv.style.cssText = loadingDiv.style.cssText;
                successDiv.innerHTML = `
                    <div style="margin-bottom: 10px;">
                        <i class="fa fa-check-circle" style="font-size: 24px; color: #4caf50;"></i>
                    </div>
                    <div>Host blacklisted successfully!</div>
                    <div style="font-size: 12px; margin-top: 5px;">Redirecting...</div>
                `;
                document.body.appendChild(successDiv);
                
                // Refresh the parent window after 2 seconds
                setTimeout(() => {
                    // If we're in an iframe, refresh the parent window
                    if (window.parent && window.parent !== window) {
                        window.parent.location.reload();
                    } else {
                        // If not in iframe, refresh current window
                        window.location.reload();
                    }
                }, 3000);
            } else {
                alert("Error blacklisting host. Please try again.");
            }
        }
    };
    xhr.send();
}

// Make the function globally accessible
window.blacklistHostApm = blacklistHostApm;

// Function to get the host action URL if not already fetched
async function getHostActionUrl(hostname) {
    // If we already have the URL cached, return it
    if (hostActionUrl) {
        return hostActionUrl;
    }
    
    // Otherwise fetch it
    const hostObjectInfoUrl = `https://${window.location.hostname}/nagios/cgi-bin/objectjson.cgi?query=host&hostname=${hostname}`;
    try {
        const response = await fetch(hostObjectInfoUrl, { credentials: 'include' });
        if (!response.ok) {
            throw new Error(`Host Object Info Failed: ${response.status}`);
        }
        const hostObjectInfo = await response.json();
        
        if (hostObjectInfo.result.type_text === 'Success' && hostObjectInfo.data.host && hostObjectInfo.data.host.action_url) {
            // Store for future use
            hostActionUrl = hostObjectInfo.data.host.action_url;
            return hostActionUrl;
        }
        return null;
    } catch (error) {
        console.error('Error fetching host action URL:', error);
        return null;
    }
}

// Function to toggle accordion visibility - UPDATED to use CSS classes
function toggleAccordion() {
    const accordion = document.getElementById('accordion');
    const result = document.getElementById('result');
    const accordionText = document.getElementById('accordion-text');
    const accordionIcon = document.getElementById('accordion-icon');
    
    accordion.classList.toggle('active');
    
    if (!accordion.classList.contains('active')) {
        // Closing the accordion - just toggle classes
        result.classList.remove('open');
        result.style.display = 'none';
        result.style.maxHeight = '0px';
        result.style.overflow = 'hidden';
        accordion.classList.add('accordion-with-margin');
        accordionText.textContent = 'View details';
        
        // Reset icon to chevron-right
        if (accordionIcon) {
            const iconElement = accordionIcon.querySelector('i');
            if (iconElement) {
                iconElement.className = 'fa fa-chevron-right';
            } else {
                accordionIcon.innerHTML = '<i class="fa fa-chevron-right" aria-hidden="true"></i>';
            }
        }
    } else {
        // Opening the accordion - just toggle classes
        result.classList.add('open');
        result.style.display = 'block';
        result.style.visibility = 'visible';
        result.style.maxHeight = 'none';
        result.style.overflow = 'visible';
        accordion.classList.remove('accordion-with-margin');
        accordionText.textContent = 'Hide details';
        
        // Change icon to chevron-down
        if (accordionIcon) {
            const iconElement = accordionIcon.querySelector('i');
            if (iconElement) {
                iconElement.className = 'fa fa-chevron-down';
            } else {
                accordionIcon.innerHTML = '<i class="fa fa-chevron-down" aria-hidden="true"></i>';
            }
        }
    }
}

// Main initialization function
document.addEventListener('DOMContentLoaded', function () {
    const hostname = new URLSearchParams(window.location.search).get('ip');
    const modal = document.getElementById('service-modal');
    const modalTitle = document.getElementById('modal-title');
    const modalBody = document.getElementById('modal-body');
    const modalClose = document.getElementById('modal-close');

    // No need to check for mobile/tablet here as we'll use CSS media queries instead

    modalClose.addEventListener('click', () => {
        modal.style.display = 'none';
        document.body.style.overflow = "auto";
    });
    window.addEventListener('click', (event) => {
        if (event.target === modal) {
            modal.style.display = 'none';
            document.body.style.overflow = "auto";
        }
    });

    // Remove the window resize event handler that reloads the page

    const hostInfoUrl = `https://${window.location.hostname}/nagios/cgi-bin/objectjson.cgi?query=host&hostname=${hostname}`;
    const fullStatusUrl = `get_full_status.php?hostname=${hostname}`;

    Promise.all([
        fetch(hostInfoUrl, {
            credentials: 'include'
        }).then(res => res.ok ? res.json() : Promise.reject(`Host Info Failed: ${res.status}`)),
        fetch(fullStatusUrl).then(res => res.ok ? res.json() : Promise.reject(`Full Status Failed: ${res.status}`))
    ]).then(([hostInfo, fullStatus]) => {
        window.apmFullStatus = fullStatus; // Store full status globally
        const hostCard = document.getElementById('host-card');
        if (hostInfo.result.type_text === 'Success' && fullStatus.hoststatus) {
            const host = hostInfo.data.host;
            const hostStats = fullStatus.hoststatus;
            
            const statusMapHostDat = {
                '0': { class: 'ok', text: 'UP' },
                '1': { class: 'critical', text: 'DOWN' },
                '2': { class: 'unknown', text: 'UNREACHABLE' }
            };

            const statusEntry = statusMapHostDat[hostStats.current_state] || {
                class: 'unknown',
                text: 'UNKNOWN'
            };
            realHostName = host.name;
            hostCard.className = `host-card ${statusEntry.class}`;
            hostCard.innerHTML = `
        <div class="host-status">
            <div class="status-dot ${statusEntry.class}"></div>
            <span class="host-name">Host Status</span>
            <span title="Host commands" class="host-options"><i class="fa fa-ellipsis-v" aria-hidden="true"></i></span>
        </div>
        <div class="host-details">
            Status: ${statusEntry.text}<br>
            Address: ${host.address}<br>
            Display Name: ${host.display_name}
            <div class="host-mac-addresses" id="host-mac-addresses"></div>
        </div>
    `;

            hostCard.addEventListener('click', () => {
                const statuses = {
                    activeChecks: hostStats.active_checks_enabled === '1' ? {
                        text: 'ENABLED',
                        class: 'ok'
                    } : {
                        text: 'DISABLED',
                        class: 'critical'
                    },
                    passiveChecks: hostStats.passive_checks_enabled === '1' ? {
                        text: 'ENABLED',
                        class: 'ok'
                    } : {
                        text: 'DISABLED',
                        class: 'critical'
                    },
                    obsessing: hostStats.obsess === '1' ? {
                        text: 'ENABLED',
                        class: 'ok'
                    } : {
                        text: 'DISABLED',
                        class: 'critical'
                    },
                    notifications: hostStats.notifications_enabled === '1' ? {
                        text: 'ENABLED',
                        class: 'ok'
                    } : {
                        text: 'DISABLED',
                        class: 'critical'
                    },
                    eventHandler: hostStats.event_handler_enabled === '1' ? {
                        text: 'ENABLED',
                        class: 'ok'
                    } : {
                        text: 'DISABLED',
                        class: 'critical'
                    },
                    flapDetection: hostStats.flap_detection_enabled === '1' ? {
                        text: 'ENABLED',
                        class: 'ok'
                    } : {
                        text: 'DISABLED',
                        class: 'critical'
                    }
                };

                modalTitle.textContent = "Host Status";
                
                // Fetch the latest host object info to get action_url
                const hostObjectInfoUrl = `https://${window.location.hostname}/nagios/cgi-bin/objectjson.cgi?query=host&hostname=${host.name}`;
                
                fetch(hostObjectInfoUrl, { credentials: 'include' })
                    .then(response => response.ok ? response.json() : Promise.reject(`Host Object Info Failed: ${response.status}`))
                    .then(hostObjectInfo => {
                        // Update host object with action_url if available
                        if (hostObjectInfo.result.type_text === 'Success' && hostObjectInfo.data.host) {
                            host.action_url = hostObjectInfo.data.host.action_url || null;
                            // Store the action_url globally for use with services
                            hostActionUrl = host.action_url;
                        }
                        
                        // All data is now from status.dat, so we don't need the old fallback logic.
                        // We do, however, need to map the hostStats from status.dat to the format populateHostModal expects.
                        const hostStatsForModal = mapHostStatsForModal(hostStats);
                        populateHostModal(host, hostStatsForModal, statuses, statusEntry);
                    })
                    .catch(error => {
                        console.error('Error fetching host object info:', error);
                        // Proceed with what we have even if object info fetch fails
                        const hostStatsForModal = mapHostStatsForModal(hostStats);
                        populateHostModal(host, hostStatsForModal, statuses, statusEntry);
                    });
            });

            updateHostCard(host.name);
            
            // Fetch and display MAC addresses
            fetchDeviceMacAddresses(host.address);

            // Add context menu functionality
            async function showHostContextMenu(e) {
                e.preventDefault();
                e.stopPropagation(); // Prevent triggering the card's click event
                const menuHTML = await generateContextMenu(null, hostname, true, hostStats);
                const contextMenu = document.getElementById('custom-context-menu');
                contextMenu.innerHTML = menuHTML;

                contextMenu.style.display = 'block';
                contextMenu.style.visibility = 'hidden';
                contextMenu.style.left = '-9999px';
                contextMenu.style.top = '0px';

                const menuWidth = contextMenu.offsetWidth;
                const menuHeight = contextMenu.offsetHeight;

                contextMenu.style.display = 'none';
                contextMenu.style.visibility = 'visible';

                let adjustedX = e.clientX;
                let adjustedY = e.clientY;

                if (adjustedX + menuWidth > window.innerWidth) adjustedX = window.innerWidth - menuWidth;
                if (adjustedY + menuHeight > window.innerHeight) adjustedY = window.innerHeight - menuHeight;

                adjustedX = Math.max(adjustedX, 0);
                adjustedY = Math.max(adjustedY, 0);

                contextMenu.style.left = `${adjustedX}px`;
                contextMenu.style.top = `${adjustedY}px`;
                contextMenu.style.display = 'block';
            }

            hostCard.addEventListener('contextmenu', showHostContextMenu);

            // Add click event listener for the ellipsis icon
            const optionsIcon = hostCard.querySelector('.host-options');
            if (optionsIcon) {
                optionsIcon.addEventListener('click', showHostContextMenu);
            }

            // --- RENDER SERVICES ---
            const statusDiv = document.getElementById('status');
            if (fullStatus.servicestatus) {
                const services = fullStatus.servicestatus;
                let servicesHTML = `<div class="services-grid">`;
                
                let hasOidError = false;

                const statusMapSvcDat = {
                    '0': { class: 'ok', text: 'OK' },
                    '1': { class: 'warning', text: 'Warning' },
                    '2': { class: 'critical', text: 'Critical' },
                    '3': { class: 'unknown', text: 'Unknown' }
                };

                for (const service of services) {
                    // Detect "pending" state: service not yet checked (has_been_checked == 0 OR last_check == 0)
                    let status;
                    const isPending = (service.has_been_checked === '0') || (service.last_check === '0');

                    if (isPending) {
                        status = { class: 'pending', text: 'Pending' };
                    } else {
                        status = statusMapSvcDat[service.current_state] || { class: 'pending', text: 'Pending' };
                    }
                    const serviceName = service.service_description;
                    const encodedServiceName = encodeURIComponent(serviceName);

                    servicesHTML += `
                    <div class="service-card ${status.class}" data-service="${encodedServiceName}">
                        <div class="service-icon-container"></div>
                        <div class="service-header">
                            <div class="status-dot ${status.class}"></div>
                            <span class="service-title">${serviceName}</span>
                            <span title="Service commands" class="service-options"><i class="fa fa-ellipsis-v" aria-hidden="true"></i></span>
                        </div>
                        <div class="service-details">
                            Status: ${status.text}<br>
                        </div>
                    </div>`;
                }

                accordion  = `
                        <button id="accordion" class="accordion" onclick="toggleAccordion()">
                            <span id="accordion-text" class="accordion-text">View details</span>
                            <span id="accordion-icon" class="accordion-icon">
                                <i class="fa fa-chevron-right" aria-hidden="true"></i>
                            </span>
                        </button>
                        <div id="result" class="result"></div>
                    `;
                servicesHTML += `</div>`;
                statusDiv.innerHTML = accordion + servicesHTML;
                document.getElementById('service-count').innerHTML = `<i class="fa fa-server" aria-hidden="true"></i> ${services.length} Services`;

                // Post-render processing for each card (icons, events)
                document.querySelectorAll('.service-card').forEach(card => {
                    const encodedServiceName = card.getAttribute('data-service');
                    const service = services.find(s => encodeURIComponent(s.service_description) === encodedServiceName);
                    if (!service) return;

                    // Add service status icons from status.dat data
                    const iconContainer = card.querySelector('.service-icon-container');
                    if (iconContainer) {
                         const iconMappings = [
                            { condition: service.active_checks_enabled === '0', iconClass: 'fa-times-circle', tooltip: 'Active checks are off' },
                            { condition: service.flap_detection_enabled === '0', iconClass: 'fa-flag', tooltip: 'Flap detection is off' },
                            { condition: service.notifications_enabled === '0', iconClass: 'fa-bell-slash', tooltip: 'Notifications are off' },
                            { condition: service.passive_checks_enabled === '0', iconClass: 'fa-eye-slash', tooltip: 'Passive checks are off' },
                            { condition: service.event_handler_enabled === '0', iconClass: 'fa-toggle-off', tooltip: 'Event handler is off' },
                            { condition: parseInt(service.scheduled_downtime_depth) !== 0, iconClass: 'fa-moon-o', tooltip: 'In schedule downtime' },
                            { condition: service.obsess === '0', iconClass: 'fa-meh-o', tooltip: 'Obsessing is off' },
                            { condition: service.problem_has_been_acknowledged === '1', iconClass: 'fa-gavel', tooltip: 'Problem has been acknowledged' }
                        ];
                        
                        iconMappings.forEach(({condition, iconClass, tooltip}) => {
                            if (condition) {
                                const icon = document.createElement('i');
                                icon.className = `fa ${iconClass} service-icon`;
                                icon.title = tooltip;
                                iconContainer.appendChild(icon);
                            }
                        });
                    }

                    // Check for performance graphs (still requires objectjson API)
                    const serviceObjectUrl = `https://${window.location.hostname}/nagios/cgi-bin/objectjson.cgi?query=service&hostname=${hostname}&servicedescription=${encodedServiceName}`;
                    fetch(serviceObjectUrl, { credentials: 'include' })
                        .then(response => response.ok ? response.json() : Promise.reject('Failed to fetch service object info'))
                        .then(serviceObjectData => {
                            if (serviceObjectData.result.type_text === 'Success' && 
                                serviceObjectData.data.service && 
                                serviceObjectData.data.service.action_url) {
                                addGraphIconToService(card, service.service_description);
                            }
                        })
                        .catch(error => console.log(`Error checking service action URL: ${error}`));


                    let longPressTimer;
                    
                    async function showContextMenu(e, card) {
                        e.preventDefault();
                        const serviceEncodedName = card.getAttribute('data-service');
                        const serviceData = services.find(s => encodeURIComponent(s.service_description) === serviceEncodedName);
                        const menuHTML = await generateContextMenu(serviceEncodedName, hostname, false, serviceData);
                        const contextMenu = document.getElementById('custom-context-menu');
                        contextMenu.innerHTML = menuHTML;

                        contextMenu.style.display = 'block';
                        contextMenu.style.visibility = 'hidden';
                        contextMenu.style.left = '-9999px';
                        contextMenu.style.top = '0px';

                        const menuWidth = contextMenu.offsetWidth;
                        const menuHeight = contextMenu.offsetHeight;

                        contextMenu.style.display = 'none';
                        contextMenu.style.visibility = 'visible';

                        const viewportX = e.clientX || e.touches[0].clientX;
                        const viewportY = e.clientY || e.touches[0].clientY;
                        const scrollX = window.scrollX || window.pageXOffset;
                        const scrollY = window.scrollY || window.pageYOffset;

                        let adjustedX = viewportX + scrollX;
                        let adjustedY = viewportY + scrollY;

                        const docWidth = Math.max(document.documentElement.clientWidth, window.innerWidth);
                        const docHeight = Math.max(document.documentElement.clientHeight, window.innerHeight);

                        if (adjustedX + menuWidth > docWidth + scrollX) adjustedX = docWidth + scrollX - menuWidth;
                        if (adjustedY + menuHeight > docHeight + scrollY) adjustedY = docHeight + scrollY - menuHeight;

                        adjustedX = Math.max(adjustedX, scrollX);
                        adjustedY = Math.max(adjustedY, scrollY);

                        contextMenu.style.left = `${adjustedX}px`;
                        contextMenu.style.top = `${adjustedY}px`;
                        contextMenu.style.display = 'block';
                    }

                    card.addEventListener('contextmenu', (e) => showContextMenu(e, card));
                    card.addEventListener('touchstart', (e) => {
                        longPressTimer = setTimeout(() => showContextMenu(e, card), 500);
                    });
                    card.addEventListener('touchend', () => clearTimeout(longPressTimer));
                    card.addEventListener('touchmove', () => clearTimeout(longPressTimer));

                    // Add click event listener for the ellipsis icon
                    const optionsIcon = card.querySelector('.service-options');
                    if (optionsIcon) {
                        optionsIcon.addEventListener('click', (e) => {
                            e.stopPropagation(); // Prevent triggering the card's click event
                            showContextMenu(e, card);
                        });
                    }

                    card.addEventListener('click', function(e) {
                        // Check if we're in selection mode
                        const servicesContainer = document.querySelector('.services-grid');
                        if (servicesContainer.classList.contains('selection-mode')) {
                            // Selection mode click is handled by toggleServiceSelection
                            return;
                        }
                        
                        // Normal mode - open service details
                        // All data is available in the 'service' object from the loop
                        if (service) {
                            // Massage data from status.dat format to the one populateServiceModal expects
                            const serviceForModal = mapServiceForModal(service);
                            populateServiceModal(serviceForModal);
                        }
                    });
                });
            } else {
                 statusDiv.style.display = 'none';
                statusDiv.innerHTML = `
                        <button id="accordion" class="accordion" onclick="toggleAccordion()">
                            <span id="accordion-text" class="accordion-text">View details</span>
                            <span id="accordion-icon" class="accordion-icon">
                                <i class="fa fa-chevron-right" aria-hidden="true"></i>
                            </span>
                        </button>
                        <div id="result" class="result"></div>
                    `;
                document.getElementById('service-count').innerHTML = `<i class="fa fa-server" aria-hidden="true"></i> 0 Services`;
                statusDiv.style.display = 'block';
            }

            // Notify listeners that host information is loaded
            document.dispatchEvent(new CustomEvent('hostLoaded', { detail: { hostname: host.name } }));
            
            // Initialize SPM functionality
            if (typeof initSpm === 'function') {
                initSpm();
            }
        } else {
            hostCard.innerHTML = `<div class="loading">This host is still being scanned. Please come back later...</div>`;
        }
    }).catch(error => {
        console.error('Fetch Error:', error);
        document.getElementById('host-card').innerHTML = `<div class="loading">Error: ${error.message}. Refreshing in 3 seconds...</div>`;
        setTimeout(() => window.location.reload(), 3000);
    });

    // This section for fetching servicelist is now replaced by get_full_status.php
    // The logic is moved inside the Promise.all().then() block above.
});

// Function to fetch and display device MAC addresses
async function fetchDeviceMacAddresses(hostIp) {
    try {
        const response = await fetch(`get_device_mac.php?ip=${encodeURIComponent(hostIp)}`);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.success && data.mac_addresses && data.mac_addresses.length > 0) {
            const macContainer = document.getElementById('host-mac-addresses');
            if (macContainer) {
                let macHtml = '<div class="mac-addresses-section">';
                macHtml += '<h4>MAC Addresses:</h4>';
                
                data.mac_addresses.forEach(macInfo => {
                    const sourceLabel = macInfo.source === 'node_ip' ? 'Interface' : 'Management';
                    const sourceIcon = macInfo.source === 'node_ip' ? 'fa-sitemap' : 'fa-cog';
                    
                    macHtml += `
                        <div class="mac-address-item">
                            <i class="fa ${sourceIcon}" title="${sourceLabel}"></i>
                            <span class="mac-label">${sourceLabel}:</span>
                            <span class="mac-value">${macInfo.mac}</span>
                        </div>
                    `;
                });
                
                macHtml += '</div>';
                macContainer.innerHTML = macHtml;
            }
        }
    } catch (error) {
        console.error('Error fetching MAC addresses:', error);
        // Silently fail - MAC addresses are not critical
    }
}