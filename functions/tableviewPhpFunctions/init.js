/**
 * Table View initialization and rendering
 * Groups hosts by hostgroup and renders each group in its own table/section.
 * Reuses existing helpers and UI patterns from the List View.
 */

// Bubble View nicknames cache
let bubbleHostnamesMap = null;

// Global cache to hold the grouped data for search/refresh
let tableViewData = {
  // hostgroupName: Array<Host>
};

document.addEventListener('DOMContentLoaded', () => {
  // Initialize shared pieces used in list view
  initFeatureStatusIndicators();
  setupPopoverHandlers();
  setupTableviewHeaderInteractions();
  setupTableviewSearch();
  setupTableviewFilters();
  setupMobileHeaderLayout();
  initContextMenu();
  setupModalEventHandlers();
  loadContextMenuScripts();
  initAutoRefresh();

  // Initial population
  refreshTableView();
});

async function refreshTableView() {
  const container = document.getElementById('tableview-content');
  if (container) {
    container.innerHTML = '<div class="hostlist-loading"><div class="spinner"></div><div>Loading grouped hosts and services...</div></div>';
  }

  try {
    // Ensure Bubble View nicknames are available for rendering
    await fetchBubbleHostnames();
    // Determine active filters from header (same behavior as list view)
    const activeHostFilters = document.querySelectorAll('.hostlist-status-filter[data-type="host"].active');
    const activeServiceFilters = document.querySelectorAll('.hostlist-status-filter[data-type="service"].active');

    const hostFilters = Array.from(activeHostFilters).map(btn => btn.getAttribute('data-status'));
    const hostFilterValue = hostFilters.length > 0 ? hostFilters : 'all';

    const serviceFilters = Array.from(activeServiceFilters).map(btn => btn.getAttribute('data-status'));
    const serviceFilterValue = serviceFilters.length > 0 ? serviceFilters : 'all';

    const hostgroupSelect = document.getElementById('hostgroup-filter');
    const servicegroupSelect = document.getElementById('servicegroup-filter');
    const selectedHostgroup = hostgroupSelect ? hostgroupSelect.value : 'all';
    const selectedServicegroup = servicegroupSelect ? servicegroupSelect.value : 'all';

    // Build combined host/service data using existing helper (already filtered by host/service/servicegroup)
    const combined = await buildHostServiceData(hostFilterValue, serviceFilterValue, selectedHostgroup, selectedServicegroup);

    // Get host->hostgroups mapping from objectjson hostlist (most accurate)
    const hostObjectAll = await fetchHostObjectData(null);

    function extractGroups(hostObj) {
      if (!hostObj || typeof hostObj !== 'object') return [];
      const hg = hostObj.hostgroups;
      if (!hg) return [];
      // Accept a variety of shapes: array of strings, array of objects, object map, comma-separated string
      if (Array.isArray(hg)) {
        return hg.map(x => {
          if (typeof x === 'string') return x;
          if (x && typeof x === 'object') return x.hostgroup_name || x.name || x.alias || Object.keys(x)[0];
          return null;
        }).filter(Boolean);
      } else if (typeof hg === 'string') {
        return hg.split(',').map(s => s.trim()).filter(Boolean);
      } else if (typeof hg === 'object') {
        // Often an object keyed by group name
        return Object.keys(hg);
      }
      return [];
    }

    // Fallback membership via hostgrouplist if hostgroups are missing
    const hostgroupMembership = await fetchHostgroupMembership();
    const allGroupNames = Object.keys(hostgroupMembership);

    const grouped = {};
    const ungrouped = [];
    combined.forEach(host => {
      const objHost = hostObjectAll?.data?.hostlist ? hostObjectAll.data.hostlist[host.name] : null;
      let groups = extractGroups(objHost);
      if ((!groups || groups.length === 0) && allGroupNames.length > 0) {
        // Try membership map
        groups = allGroupNames.filter(g => hostgroupMembership[g] && hostgroupMembership[g].has(host.name));
      }
      if (groups && groups.length) {
        groups.forEach(g => {
          if (!grouped[g]) grouped[g] = [];
          grouped[g].push(host);
        });
      } else {
        ungrouped.push(host);
      }
    });
    if (ungrouped.length > 0) grouped['Ungrouped'] = ungrouped;

    tableViewData = grouped;

    // Update status counts in filter buttons
    const statusCountsData = await fetchStatusCounts(selectedHostgroup, selectedServicegroup);
    updateStatusFilterCounts(statusCountsData);

    // Decide which groups to render
    let groupsToRender;
    if (selectedHostgroup && selectedHostgroup !== 'all') {
      groupsToRender = [selectedHostgroup];
    } else {
      // Respect API order where possible, fallback to alphabetical; put Ungrouped at the end
      const nonEmpty = Object.keys(grouped).filter(g => Array.isArray(grouped[g]) && grouped[g].length > 0);
      const ungroupedIdx = nonEmpty.indexOf('Ungrouped');
      if (ungroupedIdx >= 0) nonEmpty.splice(ungroupedIdx, 1);
      const ordered = allGroupNames.filter(g => nonEmpty.includes(g));
      const remaining = nonEmpty.filter(g => !ordered.includes(g)).sort((a,b)=>a.localeCompare(b));
      groupsToRender = [...ordered, ...remaining];
      if (grouped['Ungrouped'] && grouped['Ungrouped'].length > 0) groupsToRender.push('Ungrouped');
    }

    renderGroupedTables(groupsToRender, grouped);
  } catch (err) {
    console.error('Error refreshing table view:', err);
    if (container) {
      container.innerHTML = `<div class="hostlist-error">Error loading data: ${err.message}</div>`;
    }
  }
}

function renderGroupedTables(groupsToRender, grouped) {
  const container = document.getElementById('tableview-content');
  if (!container) return;

  if (!groupsToRender || groupsToRender.length === 0) {
    container.innerHTML = '<div class="tableview-empty">No hostgroups found</div>';
    return;
  }

  let html = '<div class="tableview-sections">';
  groupsToRender.forEach(groupName => {
    const hosts = grouped[groupName] || [];

    // Calculate quick service badges summary for header
    let svcOk = 0, svcWarn = 0, svcCrit = 0, svcUnk = 0;
    hosts.forEach(h => h.services.forEach(s => {
      if (s.status_class === 'ok') svcOk++;
      else if (s.status_class === 'warning') svcWarn++;
      else if (s.status_class === 'critical') svcCrit++;
      else if (s.status_class === 'unknown') svcUnk++;
    }));

    html += `<section class="tableview-section" data-hostgroup="${escapeHtml(groupName)}">
      <h3>
        <span>${escapeHtml(groupName)}</span>
        <span class="svc-badges">
          <span class="svc-badge ok" title="OK">${svcOk}</span>
          <span class="svc-badge warning" title="Warning">${svcWarn}</span>
          <span class="svc-badge critical" title="Critical">${svcCrit}</span>
          <span class="svc-badge unknown" title="Unknown">${svcUnk}</span>
        </span>
      </h3>
      ${generateTableHTMLTableview(hosts, false, (document.getElementById('servicegroup-filter')?.value || 'all') !== 'all')}
    </section>`;
  });
  html += '</div>';
  container.innerHTML = html;

  // No per-row feature icons in this compact table view
}

function setupTableviewHeaderInteractions() {
  // Reuse the same reset logic and multi-select behavior from list view
  const hostFilters = document.querySelectorAll('.hostlist-status-filter[data-type="host"]');
  const serviceFilters = document.querySelectorAll('.hostlist-status-filter[data-type="service"]');
  const resetAllFilters = document.getElementById('resetAllFilters');
  const hostgroupSelect = document.getElementById('hostgroup-filter');
  const servicegroupSelect = document.getElementById('servicegroup-filter');

  // Populate dropdowns
  (async () => {
    try {
      const hostgroups = await fetchHostGroups();
      if (hostgroupSelect && hostgroups.length > 0) {
        const allOption = hostgroupSelect.querySelector('option[value="all"]');
        hostgroupSelect.innerHTML = '';
        if (allOption) hostgroupSelect.appendChild(allOption);
        hostgroups.forEach(g => {
          const opt = document.createElement('option');
          opt.value = g; opt.textContent = g; hostgroupSelect.appendChild(opt);
        });
      }

      const servicegroups = await fetchServiceGroups();
      if (servicegroupSelect && servicegroups.length > 0) {
        const allOption = servicegroupSelect.querySelector('option[value="all"]');
        servicegroupSelect.innerHTML = '';
        if (allOption) servicegroupSelect.appendChild(allOption);
        servicegroups.forEach(g => {
          const opt = document.createElement('option');
          opt.value = g; opt.textContent = g; servicegroupSelect.appendChild(opt);
        });
      }
    } catch (e) {
      console.error('Failed to populate filters:', e);
    }
  })();

  resetAllFilters?.addEventListener('click', async () => {
    hostFilters.forEach(btn => btn.classList.remove('active'));
    serviceFilters.forEach(btn => btn.classList.remove('active'));
    if (hostgroupSelect) hostgroupSelect.value = 'all';
    if (servicegroupSelect) servicegroupSelect.value = 'all';
    await refreshTableView();
  });

  hostFilters.forEach(btn => {
    btn.addEventListener('click', async () => {
      serviceFilters.forEach(b => b.classList.remove('active'));
      btn.classList.toggle('active');
      await refreshTableView();
    });
  });
  serviceFilters.forEach(btn => {
    btn.addEventListener('click', async () => {
      hostFilters.forEach(b => b.classList.remove('active'));
      btn.classList.toggle('active');
      await refreshTableView();
    });
  });

  hostgroupSelect?.addEventListener('change', async () => {
    if (servicegroupSelect && hostgroupSelect.value !== 'all') {
      servicegroupSelect.value = 'all';
    }
    await refreshTableView();
  });
  servicegroupSelect?.addEventListener('change', async () => {
    if (hostgroupSelect && servicegroupSelect.value !== 'all') {
      hostgroupSelect.value = 'all';
    }
    await refreshTableView();
  });
}

function setupTableviewSearch() {
  const searchInput = document.getElementById('searchBar-tableview');
  const clearBtn = document.getElementById('tableview-clear-search');
  const searchModeSelect = document.getElementById('search-mode-tableview');

  function applySearch() {
    const mode = searchModeSelect?.value || 'host';
    const rawValue = (searchInput?.value || '').toLowerCase();
    const isExact = ((rawValue.startsWith('"') && rawValue.endsWith('"')) || (rawValue.startsWith("'") && rawValue.endsWith("'"))) && rawValue.length > 2;
    const term = isExact ? rawValue.substring(1, rawValue.length - 1).toLowerCase() : rawValue.trim();

    // If empty, just re-render full groups currently in data
    if (!term) {
      const hostgroupSelect = document.getElementById('hostgroup-filter');
      const selectedHostgroup = hostgroupSelect ? hostgroupSelect.value : 'all';
      const groups = selectedHostgroup !== 'all' ? [selectedHostgroup] : Object.keys(tableViewData);
      renderGroupedTables(groups, tableViewData);
      return;
    }

    // Build a filtered copy of tableViewData
    const filtered = {};
    Object.entries(tableViewData).forEach(([group, hosts]) => {
      const outHosts = [];
      hosts.forEach(h => {
        const hostNameLower = h.display_name.toLowerCase();
        const hostIpLower = h.address.toLowerCase();
        if (mode === 'host') {
          let match = false;
          if (isExact) match = (hostNameLower === term) || (hostIpLower === term);
          else match = hostNameLower.includes(term) || hostIpLower.includes(term);
          if (match) outHosts.push({...h});
        } else {
          const matchedServices = h.services.filter(s => {
            const svcLower = s.name.toLowerCase();
            return isExact ? svcLower === term : svcLower.includes(term);
          });
          if (matchedServices.length) outHosts.push({...h, services: matchedServices});
        }
      });
      if (outHosts.length) filtered[group] = outHosts;
    });

    const groups = Object.keys(filtered);
    if (groups.length === 0) {
      const container = document.getElementById('tableview-content');
      if (container) container.innerHTML = '<div class="tableview-empty">No matching results</div>';
    } else {
      renderGroupedTables(groups, filtered);
    }
  }

  searchInput?.addEventListener('input', applySearch);
  searchModeSelect?.addEventListener('change', () => {
    if (searchModeSelect.value === 'host') searchInput.placeholder = 'Search hosts...';
    else searchInput.placeholder = 'Search services...';
    applySearch();
  });
  clearBtn?.addEventListener('click', () => { if (searchInput) searchInput.value = ''; applySearch(); });
}

function setupTableviewFilters() {
  // Placeholder to keep parity with list view organization if needed later
}

function escapeHtml(s) {
  return String(s)
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
}

// Minimal fetch for Bubble View nicknames (IP => nickname)
async function fetchBubbleHostnames() {
  if (bubbleHostnamesMap !== null) return bubbleHostnamesMap;
  try {
    const res = await fetch('get_bubble_hostnames.php');
    if (!res.ok) throw new Error(`HTTP ${res.status}`);
    bubbleHostnamesMap = await res.json();
  } catch (e) {
    console.error('Error fetching Bubble View hostnames:', e);
    bubbleHostnamesMap = {};
  }
  return bubbleHostnamesMap;
}

// Status icon population (host/service) for tableview
function populateHostListStatusIconsTableview(data) {
  const iconMappings = {
    host: [
      { condition: (i) => i.checks_enabled === false, iconClass: 'fa-times-circle', tooltip: 'Active checks are off' },
      { condition: (i) => i.flap_detection_enabled === false, iconClass: 'fa-flag', tooltip: 'Flap detection is off' },
      { condition: (i) => i.notifications_enabled === false, iconClass: 'fa-bell-slash', tooltip: 'Notifications are off' },
      { condition: (i) => i.accept_passive_checks === false, iconClass: 'fa-eye-slash', tooltip: 'Passive checks are off' },
      { condition: (i) => i.event_handler_enabled === false, iconClass: 'fa-toggle-off', tooltip: 'Event handler is off' },
      { condition: (i) => Number(i.scheduled_downtime_depth) !== 0, iconClass: 'fa-moon-o', tooltip: 'In scheduled downtime' },
      { condition: (i) => i.obsess === false, iconClass: 'fa-meh-o', tooltip: 'Obsessing is off' },
      { condition: (i) => i.problem_has_been_acknowledged === true, iconClass: 'fa-gavel', tooltip: 'Problem has been acknowledged' }
    ],
    service: [
      { condition: (i) => i.checks_enabled === false, iconClass: 'fa-times-circle', tooltip: 'Active checks are off' },
      { condition: (i) => i.flap_detection_enabled === false, iconClass: 'fa-flag', tooltip: 'Flap detection is off' },
      { condition: (i) => i.notifications_enabled === false, iconClass: 'fa-bell-slash', tooltip: 'Notifications are off' },
      { condition: (i) => i.accept_passive_checks === false, iconClass: 'fa-eye-slash', tooltip: 'Passive checks are off' },
      { condition: (i) => i.event_handler_enabled === false, iconClass: 'fa-toggle-off', tooltip: 'Event handler is off' },
      { condition: (i) => Number(i.scheduled_downtime_depth) !== 0, iconClass: 'fa-moon-o', tooltip: 'In scheduled downtime' },
      { condition: (i) => i.obsess === false, iconClass: 'fa-meh-o', tooltip: 'Obsessing is off' },
      { condition: (i) => i.problem_has_been_acknowledged === true, iconClass: 'fa-gavel', tooltip: 'Problem has been acknowledged' }
    ]
  };

  data.forEach(host => {
    const hostIconContainer = document.querySelector(`.host-status-icons[data-host-name="${host.name}"]`);
    if (hostIconContainer) {
      hostIconContainer.innerHTML = '';
      iconMappings.host.forEach(mapping => {
        if (mapping.condition(host)) {
          const icon = document.createElement('i');
          icon.className = `fa ${mapping.iconClass} hostlist-status-icon`;
          icon.title = mapping.tooltip;
          hostIconContainer.appendChild(icon);
        }
      });
    }

    host.services.forEach(service => {
      const serviceIconContainer = document.querySelector(`.service-status-icons[data-host-name="${host.name}"][data-service-name="${service.name}"]`);
      if (serviceIconContainer) {
        serviceIconContainer.innerHTML = '';
        iconMappings.service.forEach(mapping => {
          if (mapping.condition(service)) {
            const icon = document.createElement('i');
            icon.className = `fa ${mapping.iconClass} hostlist-status-icon`;
            icon.title = mapping.tooltip;
            serviceIconContainer.appendChild(icon);
          }
        });
      }
    });
  });
}

// Render HTML for one group's host table. Always expanded rows for tableview.
function generateTableHTMLTableview(hostArray) {
  let tableHTML = `
    <table class="hostlist-table">
      <colgroup>
        <col class="col-host" style="width: 40%;">
        <col class="col-status" style="width: 25%;">
        <col class="col-service" style="width: 35%;">
      </colgroup>
      <thead>
        <tr>
          <th class="col-host">Host</th>
          <th class="col-status">Status</th>
          <th class="col-service">Services</th>
        </tr>
      </thead>
      <tbody>`;

  hostArray.forEach(host => {
    const bubbleName = bubbleHostnamesMap && bubbleHostnamesMap[host.address];
    const showBubbleName = bubbleName && bubbleName.trim() && bubbleName.toLowerCase() !== host.display_name.toLowerCase();

    const counts = countServiceStatuses(host.services || []);
    const badges = [
      counts.ok ? `<span class=\"svc-badge ok\" title=\"OK\">${counts.ok} OK</span>` : '',
      counts.warning ? `<span class=\"svc-badge warning\" title=\"Warning\">${counts.warning} WARNING</span>` : '',
      counts.critical ? `<span class=\"svc-badge critical\" title=\"Critical\">${counts.critical} CRITICAL</span>` : '',
      counts.unknown ? `<span class=\"svc-badge unknown\" title=\"Unknown\">${counts.unknown} UNKNOWN</span>` : '',
      counts.pending ? `<span class=\"svc-badge pending\" title=\"Pending\">${counts.pending} PENDING</span>` : ''
    ].filter(Boolean).join(' ');

    // If no services, show host status instead
    let serviceDisplay;
    if (!badges || badges.trim() === '') {
      // No services - show host status
      if (host.status_class === 'ok') {
        serviceDisplay = '<span class=\"svc-badge ok\" title=\"OK\">OK</span>';
      } else if (host.status_class === 'down') {
        serviceDisplay = '<span class=\"svc-badge critical\" title=\"DOWN\">DOWN</span>';
      } else if (host.status_class === 'unknown') {
        serviceDisplay = '<span class=\"svc-badge unknown\" title=\"UNREACHABLE\">UNREACHABLE</span>';
      } else {
        serviceDisplay = '<span class=\"svc-badge pending\" title=\"PENDING\">PENDING</span>';
      }
    } else {
      serviceDisplay = badges;
    }

    tableHTML += `
      <tr class=\"hostlist-host-row\" data-host-id=\"${host.name}\" data-hostname=\"${host.display_name}\" data-ip=\"${host.address}\" data-subnet=\"${host.subnet || 'External'}\" data-status=\"${host.status_class}\">\n\
        <td class=\"col-host\" title=\"${host.address} - ${host.status_text}${showBubbleName ? ` - Bubble: ${bubbleName}` : ''}\" onclick=\"openHostInModal('${host.display_name}', '${host.address}', '${host.subnet || 'External'}')\">\n\
          <div class=\"host-cell-content\">\n\
            ${host.action_url ? `<i class='fa fa-area-chart graph-icon' title='Performance data available' onclick=\"event.stopPropagation(); openHostInModal('${host.display_name}', '${host.address}', '${host.subnet || 'External'}', true, 'performance')\"></i>` : ''}\n\
            <span class=\"hostname ${host.status_class === 'down' ? 'down' : ''}\">${host.display_name}</span>\n\
          </div>\n\
        </td>\n\
        <td class=\"col-status\" onclick=\"openHostInModal('${host.display_name}', '${host.address}', '${host.subnet || 'External'}')\">\n\
          <span class=\"hostlist-status ${host.status_class}\">${host.status_text}</span>\n\
        </td>\n\
        <td class=\"col-service\" onclick=\"openHostInModal('${host.display_name}', '${host.address}', '${host.subnet || 'External'}')\">\n\
          <div class=\"svc-badges\">${serviceDisplay}</div>\n\
        </td>\n\
      </tr>`;
  });

  tableHTML += `</tbody></table>`;
  return tableHTML;
}

function countServiceStatuses(services) {
  const counts = { ok: 0, warning: 0, critical: 0, unknown: 0, pending: 0 };
  services.forEach(s => {
    if (!s || !s.status_class) return;
    if (counts[s.status_class] !== undefined) counts[s.status_class] += 1;
  });
  return counts;
}

// Expose for autoRefresh to call consistently
window.populateHostListPage = refreshTableView;

// Build hostgroup membership: { groupName: Set(hostnames) }
async function fetchHostgroupMembership() {
  try {
    const resp = await fetch(`https://${window.location.hostname}/nagios/cgi-bin/objectjson.cgi?query=hostgrouplist&details=true`);
    if (!resp.ok) throw new Error(`HTTP ${resp.status}`);
    const data = await resp.json();
    const map = {};
    if (data?.data?.hostgrouplist) {
      Object.entries(data.data.hostgrouplist).forEach(([groupName, groupObj]) => {
        const members = new Set();
        const raw = groupObj.members;
        if (Array.isArray(raw)) {
          raw.forEach(m => {
            if (typeof m === 'string') members.add(m);
            else if (m && typeof m === 'object' && m.host_name) members.add(m.host_name);
          });
        } else if (raw && typeof raw === 'object') {
          Object.values(raw).forEach(m => {
            if (typeof m === 'string') members.add(m);
            else if (m && typeof m === 'object' && m.host_name) members.add(m.host_name);
          });
        }
        map[groupName] = members;
      });
    }
    return map;
  } catch (e) {
    console.error('Failed to fetch hostgroup membership:', e);
    return {};
  }
}

