<?php
// src/hostRelationship/getSpeedInfo.php

include "../../loadenv.php";

// Check if SPM module is available (like in get_spm_data.php)
$spm_check = shell_exec("php " . __DIR__ . "/../../checkmods.php spm 2>&1");
if (trim($spm_check) !== "true") {
    http_response_code(500);
    echo json_encode(['error' => 'SPM module is not available']);
    exit;
}

// Prevent caching
header("Cache-Control: no-cache, must-revalidate");
header("Content-Type: application/json");

try {
    // Get parent and child IPs from request
    $parentIp = $_GET['parent_ip'] ?? '';
    $childIp = $_GET['child_ip'] ?? '';
    
    if (empty($parentIp) || empty($childIp)) {
        http_response_code(400);
        echo json_encode(['error' => 'Parent IP and Child IP are required']);
        exit;
    }
    
    // Build the Netdisco query to get speed information
    // Use a simpler approach that matches the logic in get_spm_data.php
    $sql = "SELECT dp.ip AS parent_ip, dp.speed AS port_speed FROM device_port dp LEFT JOIN node n ON dp.ip = n.switch AND dp.port = n.port AND n.time_last > NOW() - INTERVAL '2 hours' WHERE dp.ip = '$parentIp' AND (dp.remote_ip = '$childIp' OR EXISTS (SELECT 1 FROM node_ip WHERE mac = n.mac AND active = true AND ip = '$childIp'))";
    
    // Execute the query via psql with proper formatting options
    // Use the sudoers configuration: apache ALL=(netdisco) NOPASSWD: /usr/bin/psql -U netdisco -d netdisco *
    $command = "sudo -u netdisco /usr/bin/psql -U netdisco -d netdisco -t -A -F'|' -c " . escapeshellarg($sql);
    
    $output = shell_exec($command . ' 2>&1');
    
    if ($output === null) {
        throw new Exception('Failed to execute database query');
    }
    
    // Parse the output
    $lines = array_filter(explode("\n", trim($output)));
    
    $speedInfo = null;
    
    foreach ($lines as $line) {
        $line = trim($line);
        if ($line === '') continue;
        
        // Parse the pipe-separated line
        $parts = explode('|', $line);
        
        if (count($parts) >= 2) {
            $speedInfo = [
                'parent_ip' => trim($parts[0]),
                'child_ip' => $childIp, // Use the input parameter
                'port_speed' => trim($parts[1])
            ];
            break; // Take the first match
        }
    }
    
    if ($speedInfo) {
        echo json_encode($speedInfo);
    } else {
        // Add debugging information
        echo json_encode([
            'error' => 'No speed information found', 
            'parent_ip' => $parentIp, 
            'child_ip' => $childIp,
            'query' => $sql,
            'output_lines' => count($lines)
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Database query failed: ' . $e->getMessage()]);
}
?>
