<?php
// --- Configuration ---
// List of services to monitor and manage.
// IMPORTANT: Ensure these service names match the systemd service unit files
// AND the entries you added to the sudoers file.
// Define modules and their services
$all_modules = [
    'APM' => ['nagios', 'npcd'],
    'NTA' => ['nprobe', 'ntopng'],
    'ELM' => ['opensearch', 'opensearch-dashboards', 'logstash'],
    'SPM' => ['netdisco-web', 'netdisco-backend'],
    'NSM' => ['gsad', 'gvmd', 'ospd-openvas'],
    'ALM' => ['glpi-agent'],
    'Database' => ['mariadb', 'redis', 'postgresql'],
    'Time Service' => ['chronyd'],
    'Log Service' => ['rsyslog'],
    'Remote Access' => ['shellinaboxd', 'sshd'],
    'Standalone' => ['crond', 'httpd']
];

// Service display name mapping (frontend names)
$service_display_names = [
    'nagios' => 'apm-process',
    'npcd' => 'apm-graph',
    'nprobe' => 'nta-probe',
    'ntopng' => 'nta-process',
    'opensearch' => 'elm-process',
    'opensearch-dashboards' => 'elm-dashboards',
    'logstash' => 'elm-filters',
    'netdisco-web' => 'spm-web',
    'netdisco-backend' => 'spm-backend',
    'gsad' => 'nsm-gui',
    'gvmd' => 'nsm-process',
    'ospd-openvas' => 'nsm-filter',
    'glpi-agent' => 'alm-agent'
];

// Get available modules using checkmods.php
function getAvailableModules() {
    $command = 'php ../../checkmods.php -a';
    $output = shell_exec($command . ' 2>&1');
    
    if (!$output) {
        return [];
    }
    
    // Parse the output and keep uppercase for consistency with module names
    $modules = array_filter(explode("\n", trim($output)));
    return $modules;
}

// Filter modules based on availability
$available_modules = getAvailableModules();
$modules = [];

foreach ($all_modules as $module_name => $services) {
    // Always include non-module sections (Database, Time Service, etc.)
    if (!in_array(strtolower($module_name), ['apm', 'npm', 'nta', 'spm', 'elm', 'ncm', 'nsm', 'alm'])) {
        $modules[$module_name] = $services;
    } else {
        // Only include module sections if they're available
        if (in_array($module_name, $available_modules)) {
            $modules[$module_name] = $services;
        }
    }
}

// Create a flat list of all managed services for validation
$all_services = [];
foreach ($modules as $module_services) {
    $all_services = array_merge($all_services, $module_services);
}
$all_services = array_unique($all_services);

// Path to systemctl command (usually correct for RHEL-based systems)
$systemctl_path = '/usr/bin/systemctl';
$sudo_path = '/usr/bin/sudo'; // Make sure sudo is in the path for the web user

// --- Function to get service status ---
function get_service_status($service_name, $systemctl_path, $sudo_path) {
    global $all_services; // Ensure access if needed inside, though not currently used here
    // Use 'is-active' for a simple status check
    $command = $sudo_path . ' ' . $systemctl_path . ' is-active ' . escapeshellarg($service_name);
    $status = trim(shell_exec($command . ' 2>/dev/null')); // Suppress stderr

    if ($status === 'active') {
        return ['text' => 'Running', 'class' => 'status-running'];
    } elseif ($status === 'inactive') {
        return ['text' => 'Stopped', 'class' => 'status-stopped'];
    } elseif ($status === 'failed') {
        return ['text' => 'Failed', 'class' => 'status-failed'];
    } elseif ($status === 'activating') {
         return ['text' => 'Activating', 'class' => 'status-other'];
    } else {
        // Try full status for more details if 'is-active' is inconclusive or fails
        $full_status_command = $sudo_path . ' ' . $systemctl_path . ' status ' . escapeshellarg($service_name);
        exec($full_status_command . ' 2>&1', $full_output, $full_return_var);

        if ($full_return_var === 3) {
             $check_exists_cmd = $sudo_path . ' ' . $systemctl_path . ' list-units --full --all | grep -Fq ' . escapeshellarg($service_name . '.service');
             exec($check_exists_cmd, $exists_output, $exists_return_var);
             if ($exists_return_var === 0) {
                 return ['text' => 'Stopped', 'class' => 'status-stopped'];
             } else {
                 return ['text' => 'Not Found/Error', 'class' => 'status-unknown', 'details' => 'Service unit not found or error checking status. Output: ' . htmlspecialchars(implode("\n", $full_output))];
             }
        } elseif ($full_return_var !== 0) {
             return ['text' => 'Unknown/Error', 'class' => 'status-unknown', 'details' => 'Could not get status. Check sudoers/service name. Output: ' . htmlspecialchars(implode("\n", $full_output))];
        } else {
             return ['text' => 'Unknown (' . htmlspecialchars($status) . ')', 'class' => 'status-unknown', 'details' => 'Status command succeeded but state unclear. Output: ' . htmlspecialchars(implode("\n", $full_output))];
        }
    }
}

// --- Security Check ---
// This basic script has NO authentication. Add authentication here for production use!
// Example:
// session_start();
// if (!isset($_SESSION['user_logged_in']) || $_SESSION['user_logged_in'] !== true) {
//     header("HTTP/1.1 403 Forbidden");
//     die("Access Denied. Please log in.");
// }

// --- Determine Action Type ---
$is_ajax = (
    !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
    strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'
);

// Set headers for AJAX response
if ($is_ajax) {
    // Prevent any output buffering that might be enabled
    while (ob_get_level()) {
        ob_end_clean();
    }
    
    // Set JSON content type and no-cache headers
    header('Content-Type: application/json');
    header('Cache-Control: no-cache, no-store, must-revalidate');
    header('Pragma: no-cache');
    header('Expires: 0');
}

// --- Action Handling ---
$action_message = '';
$action_error = false;
$bulk_action_results = []; // To store results of module actions

// --- Function to execute a single service action ---
function execute_service_action($service, $action, $systemctl_path, $sudo_path) {
    global $all_services; // Access the global list
    $result = ['success' => false, 'message' => '', 'service' => $service, 'action' => $action];

    // Validate service and action again just in case
    if (!in_array($service, $all_services)) {
        $result['message'] = "Invalid service '$service'.";
        return $result;
    }
    if (!in_array($action, ['start', 'stop', 'restart'])) {
        $result['message'] = "Invalid action '$action'.";
        return $result;
    }

    $command = $sudo_path . ' ' . $systemctl_path . ' ' . escapeshellarg($action) . ' ' . escapeshellarg($service) . ' 2>&1';
    $output = [];
    $return_var = -1;
    exec($command, $output, $return_var);

    if ($return_var === 0) {
        $result['success'] = true;
        $result['message'] = "Service '" . htmlspecialchars($service) . "' action '" . htmlspecialchars($action) . "' successful.";
    } else {
        $result['message'] = "Error on service '" . htmlspecialchars($service) . "' action '" . htmlspecialchars($action) . "': " . htmlspecialchars(implode("\n", $output));
    }
    return $result;
}

try {
    $action = $_GET['action'] ?? null;
    $service = $_GET['service'] ?? null;
    $module_name_param = $_GET['module'] ?? null;

    // --- Handle Module Actions ---
    if (in_array($action, ['start_module', 'stop_module', 'restart_module']) && isset($modules[$module_name_param])) {
        $actual_action = str_replace('_module', '', $action); // 'start', 'stop', 'restart'
        $services_in_module = $modules[$module_name_param];
        $success_count = 0;
        $error_count = 0;

        foreach ($services_in_module as $svc) {
            $result = execute_service_action($svc, $actual_action, $systemctl_path, $sudo_path);
            $bulk_action_results[] = $result;
            if ($result['success']) {
                $success_count++;
            } else {
                $error_count++;
            }
            // Small delay between actions might be wise for some services
            usleep(500000); // 0.5 second delay
        }

        $action_message = "Module '" . htmlspecialchars($module_name_param) . "' action '" . htmlspecialchars($actual_action) . "' attempted. Success: $success_count, Errors: $error_count.";
        $action_error = ($error_count > 0);
        sleep(2); // Wait after all actions

        if ($is_ajax) {
            echo json_encode([
                'success' => !$action_error,
                'message' => $action_message,
                'details' => $bulk_action_results,
                'module' => $module_name_param
            ]);
            exit;
        }
    }
    // --- Handle Single Service Actions ---
    elseif (in_array($action, ['start', 'stop', 'restart']) && $service) {
        $result = execute_service_action($service, $action, $systemctl_path, $sudo_path);
        $action_message = $result['message'];
        $action_error = !$result['success'];
        sleep(2); // Allow service status to update

        if ($is_ajax) {
            echo json_encode([
                'success' => !$action_error,
                'message' => $action_message,
                'error' => $action_error,
                'service' => $service,
                'action' => $action
            ]);
            exit;
        }
    }
    // --- Handle Status Check Requests ---
    elseif ($action === 'get_status' && $service) {
        if (in_array($service, $all_services)) {
            $status_info = get_service_status($service, $systemctl_path, $sudo_path);
            if ($is_ajax) {
                echo json_encode($status_info);
                exit;
            }
        } else {
            if ($is_ajax) {
                http_response_code(400);
                echo json_encode(['error' => "Invalid service name '$service' for status check."]);
                exit;
            }
        }
    }
    // --- Fallback for unrecognized AJAX requests ---
    elseif ($is_ajax) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => "Unrecognized action '$action'",
            'error' => true
        ]);
        exit;
    }

} catch (Throwable $e) {
    error_log("Error in systemServices.php: " . $e->getMessage() . "\n" . $e->getTraceAsString());
    if ($is_ajax) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'An internal server error occurred. Please check server logs.',
            'error_details' => $e->getMessage()
        ]);
        exit;
    } else {
        $action_message = "An internal server error occurred. Please contact the administrator.";
        $action_error = true;
    }
}

// Only output HTML if not an AJAX request
if (!$is_ajax) {
    // ... rest of the HTML output code ...
    ?>
    <h2><i class="fa fa-cogs"></i> Service Status</h2>

    <?php if ($action_message): ?>
        <div class="message <?php echo $action_error ? 'message-error' : 'message-success'; ?>" style="display: block;"> <!-- Ensure message div is visible -->
            <?php echo $action_message; ?>
            <?php // Optionally display detailed bulk results for non-AJAX
                if (!empty($bulk_action_results) && !$is_ajax) {
                    echo '<ul style="margin-top: 10px; font-size: 0.9em; list-style: disc; padding-left: 20px;">'; // Added list style
                    foreach ($bulk_action_results as $res) {
                        $color = $res['success'] ? 'var(--success)' : 'var(--critical)';
                        echo '<li style="color: ' . $color . '; margin-bottom: 3px;">' . htmlspecialchars($res['message']) . '</li>'; // Added margin
                    }
                    echo '</ul>';
                }
            ?>
        </div>
    <?php endif; ?>

    <div class="service-modules-container">
        <?php foreach ($modules as $module_name => $module_services):
            $module_id = str_replace(' ', '-', strtolower($module_name)); // Create a simple ID
        ?>
            <details class="module-section" id="module-<?php echo $module_id; ?>"> <!-- Removed 'open' attribute -->
                <summary class="module-header">
                    <span class="module-name"><?php echo htmlspecialchars($module_name); ?>
                    <?php 
                        // Calculate initial module status
                        $runningCount = 0;
                        $failedCount = 0;
                        $totalServices = count($module_services);
                        
                        foreach ($module_services as $svc) {
                            $svc_status = get_service_status($svc, $systemctl_path, $sudo_path);
                            if ($svc_status['text'] === 'Running') {
                                $runningCount++;
                            } else if ($svc_status['text'] === 'Failed') {
                                $failedCount++;
                            }
                        }
                        
                        // Determine status class and text
                        $statusClass = 'status-unknown';
                        $statusText = 'Unknown';
                        
                        if ($failedCount > 0) {
                            $statusClass = 'status-failed';
                            $statusText = "$runningCount/$totalServices Running (Issues)";
                        } else if ($runningCount === 0) {
                            $statusClass = 'status-stopped';
                            $statusText = 'All stopped';
                        } else if ($runningCount === $totalServices) {
                            $statusClass = 'status-running';
                            $statusText = 'All running';
                        } else {
                            $statusClass = 'status-other';
                            $statusText = "$runningCount/$totalServices Running";
                        }
                    ?>
                    <span class="module-status-indicator <?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                    </span>
                    <span class="module-actions">
                         <!-- Module Action Buttons -->
                         <form class="module-action-form" method="GET" style="display: inline;">
                             <input type="hidden" name="action" value="stop_module">
                             <input type="hidden" name="module" value="<?php echo htmlspecialchars($module_name); ?>">
                             <button type="submit" class="btn-module-action btn-stop" title="Stop all services in <?php echo htmlspecialchars($module_name); ?>">Stop All</button>
                         </form>
                         <form class="module-action-form" method="GET" style="display: inline;">
                             <input type="hidden" name="action" value="restart_module">
                             <input type="hidden" name="module" value="<?php echo htmlspecialchars($module_name); ?>">
                             <button type="submit" class="btn-module-action btn-restart" title="Restart all services in <?php echo htmlspecialchars($module_name); ?>">Restart All</button>
                         </form>
                    </span>
                </summary>
                <table class="service-status module-table">
                    <thead>
                        <tr>
                            <th>Service</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($module_services as $service): ?>
                            <?php
                                // Fetch status for each service
                                $status_info = get_service_status($service, $systemctl_path, $sudo_path);
                                $status_text = $status_info['text'];
                                $status_class = $status_info['class'];
                                $details = isset($status_info['details']) ? $status_info['details'] : '';
                                
                                // Get display name for the service
                                $display_name = isset($service_display_names[$service]) ? $service_display_names[$service] : $service;
                            ?>
                            <tr data-service="<?php echo htmlspecialchars($service); ?>">
                                <td><?php echo htmlspecialchars($display_name); ?></td>
                                <td>
                                    <span class="<?php echo $status_class; ?> status-indicator"><?php echo $status_text; ?></span>
                                    <?php if ($details): ?>
                                        <br><small><i><?php echo $details; ?></i></small>
                                    <?php endif; ?>
                                </td>
                                <td class="action-buttons">
                                    <?php
                                        // Ensure base_url is correctly determined if needed, or use relative paths for forms
                                        // Using relative path for simplicity here, assuming the script handles its own requests
                                        $base_url = ''; // Or $_SERVER['PHP_SELF'] if needed
                                    ?>
                                    <form class="service-action-form" method="GET" style="display: inline;">
                                        <input type="hidden" name="action" value="stop">
                                        <input type="hidden" name="service" value="<?php echo htmlspecialchars($service); ?>">
                                        <button type="submit" class="btn-stop" <?php if ($status_text !== 'Running') echo 'disabled'; ?>>Stop</button>
                                    </form>
                                    <form class="service-action-form" method="GET" style="display: inline;">
                                        <input type="hidden" name="action" value="restart">
                                        <input type="hidden" name="service" value="<?php echo htmlspecialchars($service); ?>">
                                        <button type="submit" class="btn-restart">Restart</button>
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </details> <!-- End module-section -->
        <?php endforeach; ?>
    </div> <!-- End service-modules-container -->
    <?php
}

?>
